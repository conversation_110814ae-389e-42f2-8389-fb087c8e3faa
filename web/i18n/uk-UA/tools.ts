const translation = {
  title: 'Інструменти',
  createCustomTool: 'Створити власний інструмент',
  type: {
    all: 'Усі',
    builtIn: 'Вбудовані',
    custom: 'Користувацькі',
    workflow: 'Робочий процес',
  },
  contribute: {
    line1: 'Мені цікаво зробити свій внесок',
    line2: 'створення інструментів для Dify.',
    viewGuide: 'Переглянути інструкцію',
  },
  author: 'Автор',
  auth: {
    authorized: 'Авторизовано',
    setup: 'Налаштувати авторизацію, щоб використовувати',
    setupModalTitle: 'Налаштування авторизації',
    setupModalTitleDescription: 'Після налаштування облікових даних усі члени робочого простору можуть використовувати цей інструмент під час оркестрування програм.',
  },
  includeToolNum: '{{num}} інструмент(ів) включено',
  addTool: 'Додати інструмент ',
  createTool: {
    title: 'Створити власний інструмент',
    editAction: 'Налаштування',
    editTitle: 'Редагувати настроюваний інструмент',
    name: 'Назва',
    toolNamePlaceHolder: 'Введіть назву інструменту',
    schema: 'Схема',
    schemaPlaceHolder: 'Введіть свою схему OpenAPI тут',
    viewSchemaSpec: 'Переглянути специфікацію OpenAPI-Swagger',
    importFromUrl: 'Імпортувати з URL-адреси',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Введіть дійсну URL-адресу',
    examples: 'Приклади',
    exampleOptions: {
      json: 'Погода (JSON)',
      yaml: 'Зоотоварів (YAML)',
      blankTemplate: 'Чистий шаблон',
    },
    availableTools: {
      title: 'Доступні інструменти',
      name: 'Назва',
      description: 'Опис',
      method: 'Метод',
      path: 'Шлях',
      action: 'Дія',
      test: 'Перевірка',
    },
    authMethod: {
      title: 'Метод авторизації',
      type: 'Тип авторизації',
      keyTooltip: 'Ключ HTTP-заголовка. Якщо ви не знаєте, залиште його як "Authorization" або встановіть власне значення',
      types: {
        none: 'Відсутня',
        apiKeyPlaceholder: 'Назва HTTP-заголовка для API-ключа',
        apiValuePlaceholder: 'Введіть API-ключ',
        api_key_header: 'Заголовок',
        queryParamPlaceholder: 'Назва параметра запиту для API Ключа',
        api_key_query: 'Параметр запиту',
      },
      key: 'Ключ',
      value: 'Значення',
      queryParam: 'Параметр запиту',
      queryParamTooltip: 'Ім\'я параметра запиту для ключа API, який потрібно передати, наприклад, "key" в "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      types: {
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Custom',
      },
      title: 'Тип аутентифікації',
    },
    privacyPolicy: 'Політика конфіденційності',
    privacyPolicyPlaceholder: 'Введіть політику конфіденційності',
    customDisclaimer: 'Власний відомості',
    customDisclaimerPlaceholder: 'Введіть власні відомості',
    deleteToolConfirmTitle: 'Видалити цей інструмент?',
    deleteToolConfirmContent: 'Видалення інструменту є незворотнім. Користувачі більше не зможуть отримати доступ до вашого інструменту.',
    toolInput: {
      label: 'Мітки',
      name: 'Ім\'я',
      required: 'Необхідний',
      method: 'Метод',
      title: 'Введення інструменту',
      methodSetting: 'Параметр',
      description: 'Опис',
      methodParameter: 'Параметр',
      labelPlaceholder: 'Виберіть теги (необов\'язково)',
      descriptionPlaceholder: 'Опис значення параметра',
      methodSettingTip: 'Користувач заповнює конфігурацію інструменту',
      methodParameterTip: 'LLM заповнюється під час логічного висновку',
    },
    description: 'Опис',
    nameForToolCall: 'Ім\'я виклику інструменту',
    confirmTitle: 'Підтвердьте, щоб зберегти?',
    nameForToolCallTip: 'Підтримує лише цифри, літери та підкреслення.',
    confirmTip: 'Це вплине на програми, які використовують цей інструмент',
    nameForToolCallPlaceHolder: 'Використовується для розпізнавання машин, таких як getCurrentWeather, list_pets',
    descriptionPlaceholder: 'Короткий опис призначення інструменту, наприклад, отримання температури для конкретного місця.',
  },
  test: {
    title: 'Тест',
    parametersValue: 'Параметри та значення',
    parameters: 'Параметри',
    value: 'Значення',
    testResult: 'Результати тесту',
    testResultPlaceholder: 'Результат тесту буде відображатися тут',
  },
  thought: {
    using: 'Використання',
    used: 'Використано',
    requestTitle: 'Запит до',
    responseTitle: 'Відповідь від',
  },
  setBuiltInTools: {
    info: 'Інформація',
    setting: 'Налаштування',
    toolDescription: 'Опис інструменту',
    parameters: 'Параметри',
    string: 'Рядок',
    number: 'Число',
    required: 'Обов’язково',
    infoAndSetting: 'Інформація та налаштування',
    file: 'файл',
  },
  noCustomTool: {
    title: 'Немає користувацьких інструментів!',
    content: 'Додавайте та керуйте своїми власними інструментами тут для створення програм зі штучним інтелектом.',
    createTool: 'Створити інструмент',
  },
  noSearchRes: {
    title: 'Вибачте, немає результатів!',
    content: 'Ми не знайшли жодних інструментів, які б відповідали вашому пошуку.',
    reset: 'Скинути пошук',
  },
  builtInPromptTitle: 'Підказка',
  toolRemoved: 'Інструмент видалено',
  notAuthorized: 'Інструмент не авторизовано',
  howToGet: 'Як отримати',
  addToolModal: {
    category: 'категорія',
    add: 'Додати',
    added: 'Додано',
    type: 'тип',
    manageInTools: 'Керування в інструментах',
    custom: {
      title: 'Немає доступного користувацького інструмента',
      tip: 'Створити користувацький інструмент',
    },
    workflow: {
      title: 'Немає доступного інструмента робочого процесу',
      tip: 'Опублікуйте робочі процеси як інструменти в Studio',
    },
    mcp: {
      title: 'Немає доступного інструмента MCP',
      tip: 'Додати сервер MCP',
    },
    agent: {
      title: 'Немає доступної стратегії агента',
    },
  },
  openInStudio: 'Відкрити в Студії',
  customToolTip: 'Дізнайтеся більше про користувацькі інструменти Dify',
  toolNameUsageTip: 'Ім\'я виклику інструменту для міркувань і підказок агента',
  copyToolName: 'Ім\'я копії',
  noTools: 'Інструментів не знайдено',
  mcp: {
    create: {
      cardTitle: 'Додати сервер MCP (HTTP)',
      cardLink: 'Дізнатися більше про інтеграцію сервера MCP',
    },
    noConfigured: 'Сервер не налаштовано',
    updateTime: 'Оновлено',
    toolsCount: '{count} інструментів',
    noTools: 'Інструменти відсутні',
    modal: {
      title: 'Додати сервер MCP (HTTP)',
      editTitle: 'Редагувати сервер MCP (HTTP)',
      name: 'Назва та значок',
      namePlaceholder: 'Назвіть ваш сервер MCP',
      serverUrl: 'URL сервера',
      serverUrlPlaceholder: 'URL кінцевої точки сервера',
      serverUrlWarning: 'Оновлення адреси сервера може порушити роботу додатків, що залежать від нього',
      serverIdentifier: 'Ідентифікатор сервера',
      serverIdentifierTip: 'Унікальний ідентифікатор сервера MCP у робочому просторі. Лише малі літери, цифри, підкреслення та дефіси. До 24 символів.',
      serverIdentifierPlaceholder: 'Унікальний ідентифікатор, напр. my-mcp-server',
      serverIdentifierWarning: 'Після зміни ID існуючі додатки не зможуть розпізнати сервер',
      cancel: 'Скасувати',
      save: 'Зберегти',
      confirm: 'Додати та Авторизувати',
      timeout: 'Час вичерпано',
      sseReadTimeout: 'Тайм-аут читання SSE',
    },
    delete: 'Видалити сервер MCP',
    deleteConfirmTitle: 'Видалити {mcp}?',
    operation: {
      edit: 'Редагувати',
      remove: 'Видалити',
    },
    authorize: 'Авторизувати',
    authorizing: 'Авторизація...',
    authorizingRequired: 'Потрібна авторизація',
    authorizeTip: 'Після авторизації інструменти відображатимуться тут.',
    update: 'Оновити',
    updating: 'Оновлення...',
    gettingTools: 'Отримання інструментів...',
    updateTools: 'Оновлення інструментів...',
    toolsEmpty: 'Інструменти не завантажено',
    getTools: 'Отримати інструменти',
    toolUpdateConfirmTitle: 'Оновити список інструментів',
    toolUpdateConfirmContent: 'Оновлення списку інструментів може вплинути на існуючі додатки. Продовжити?',
    toolsNum: '{count} інструментів включено',
    onlyTool: '1 інструмент включено',
    identifier: 'Ідентифікатор сервера (Натисніть, щоб скопіювати)',
    server: {
      title: 'Сервер MCP',
      url: 'URL сервера',
      reGen: 'Згенерувати URL сервера знову?',
      addDescription: 'Додати опис',
      edit: 'Редагувати опис',
      modal: {
        addTitle: 'Додайте опис для активації сервера MCP',
        editTitle: 'Редагувати опис',
        description: 'Опис',
        descriptionPlaceholder: 'Поясніть функціонал інструменту та його використання LLM',
        parameters: 'Параметри',
        parametersTip: 'Додайте описи параметрів, щоб допомогти LLM зрозуміти їх призначення та обмеження.',
        parametersPlaceholder: 'Призначення та обмеження параметра',
        confirm: 'Активувати сервер MCP',
      },
      publishTip: 'Додаток не опубліковано. Спочатку опублікуйте додаток.',
    },
  },
}

export default translation
