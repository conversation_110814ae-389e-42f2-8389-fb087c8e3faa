const translation = {
  title: '<PERSON><PERSON><PERSON>',
  createCustomTool: 'Ustvari prilagojeno orodje',
  customToolTip: 'Izvedite več o prilagojenih orodjih Dify',
  type: {
    all: 'Vsa',
    builtIn: 'V<PERSON>jena',
    custom: 'P<PERSON>ago<PERSON><PERSON>',
    workflow: '<PERSON><PERSON><PERSON> dela',
  },
  contribute: {
    line1: '<PERSON>anima me ',
    line2: 'prispevanje orodij k Dify.',
    viewGuide: 'Oglejte si vodič',
  },
  author: 'Avtor',
  auth: {
    authorized: 'Avtorizirano',
    setup: 'Nastavite avtorizacijo za uporabo',
    setupModalTitle: 'Nastavi avtorizacijo',
    setupModalTitleDescription: 'Po konfiguraciji poverilnic bodo vsi člani znotraj delovnega prostora lahko uporabljali to orodje pri orkestraciji aplikacij.',
  },
  includeToolNum: 'Vkl<PERSON><PERSON><PERSON> {{num}} orodij',
  addTool: '<PERSON><PERSON><PERSON> orodje',
  addToolModal: {
    type: 'tip',
    category: 'kategorija',
    add: 'dodaj',
    added: 'dodano',
    manageInTools: 'Upravljaj v Orodjih',
    custom: {
      title: 'Žiadne prispôsobené nástroje nie sú k dispozícii',
      tip: 'Vytvorte prispôsobený nástroj',
    },
    workflow: {
      title: 'Žiadny nástroj pracovného postupu nie je k dispozícii',
      tip: 'Publikujte pracovné postupy ako nástroje v Studio',
    },
    mcp: {
      title: 'Žiadny nástroj MCP nie je k dispozícii',
      tip: 'Pridajte server MCP',
    },
    agent: {
      title: 'Žiadna stratégia agenta nie je k dispozícii',
    },
  },
  createTool: {
    title: 'Ustvari prilagojeno orodje',
    editAction: 'Konfiguriraj',
    editTitle: 'Uredi prilagojeno orodje',
    name: 'Ime',
    toolNamePlaceHolder: 'Vnesite ime orodja',
    nameForToolCall: 'Ime za klic orodja',
    nameForToolCallPlaceHolder: 'Uporablja se za strojno prepoznavo, na primer getCurrentWeather, list_pets',
    nameForToolCallTip: 'Podprte so samo številke, črke in podčrtaji.',
    description: 'Opis',
    descriptionPlaceholder: 'Kratek opis namena orodja, npr. pridobi temperaturo za določeno lokacijo.',
    schema: 'Shema',
    schemaPlaceHolder: 'Vnesite svojo OpenAPI shemo tukaj',
    viewSchemaSpec: 'Oglejte si OpenAPI-Swagger specifikacijo',
    importFromUrl: 'Uvozi iz URL-ja',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Prosimo, vnesite veljaven URL',
    examples: 'Primeri',
    exampleOptions: {
      json: 'Vreme(JSON)',
      yaml: 'Trgovina za domače ljubljenčke(YAML)',
      blankTemplate: 'Prazna predloga',
    },
    availableTools: {
      title: 'Razpoložljiva orodja',
      name: 'Ime',
      description: 'Opis',
      method: 'Metoda',
      path: 'Pot',
      action: 'Dejanja',
      test: 'Testiraj',
    },
    authMethod: {
      title: 'Metoda avtorizacije',
      type: 'Vrsta avtorizacije',
      keyTooltip: 'Ključ HTTP glave, pustite kot "Authorization", če ne veste, kaj je to, ali pa nastavite na vrednost po meri',
      types: {
        none: 'Brez',
        apiKeyPlaceholder: 'Ime HTTP glave za API ključ',
        apiValuePlaceholder: 'Vnesite API ključ',
        api_key_query: 'Vprašanje Param',
        queryParamPlaceholder: 'Ime poizvedbenega parametra za API ključ',
        api_key_header: 'Naslov',
      },
      key: 'Ključ',
      value: 'Vrednost',
      queryParam: 'Parametri poizvedbe',
      queryParamTooltip: 'Ime poizvedbenega parametra API ključa, ki ga je treba posredovati, npr. "key" v "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Vrsta avtorizacije',
      types: {
        basic: 'Osnovna',
        bearer: 'Imetnik',
        custom: 'Prilagojena',
      },
    },
    privacyPolicy: 'Politika zasebnosti',
    privacyPolicyPlaceholder: 'Prosimo, vnesite politiko zasebnosti',
    toolInput: {
      title: 'Vnos orodja',
      name: 'Ime',
      required: 'Obvezno',
      method: 'Metoda',
      methodSetting: 'Nastavitve',
      methodSettingTip: 'Uporabnik izpolni konfiguracijo orodja',
      methodParameter: 'Parameter',
      methodParameterTip: 'LLM izpolni med sklepanjem',
      label: 'Oznake',
      labelPlaceholder: 'Izberite oznake (neobvezno)',
      description: 'Opis',
      descriptionPlaceholder: 'Opis pomena parametra',
    },
    customDisclaimer: 'Prilagojeno zavrnitev odgovornosti',
    customDisclaimerPlaceholder: 'Prosimo, vnesite prilagojeno zavrnitev odgovornosti',
    confirmTitle: 'Potrditi shranjevanje?',
    confirmTip: 'Aplikacije, ki uporabljajo to orodje, bodo vplivane',
    deleteToolConfirmTitle: 'Izbrisati to orodje?',
    deleteToolConfirmContent: 'Brisanje orodja je nepovratno. Uporabniki ne bodo več imeli dostopa do vašega orodja.',
  },
  test: {
    title: 'Test',
    parametersValue: 'Parametri in vrednosti',
    parameters: 'Parametri',
    value: 'Vrednost',
    testResult: 'Rezultati testa',
    testResultPlaceholder: 'Rezultati testa bodo prikazani tukaj',
  },
  thought: {
    using: 'Uporablja se',
    used: 'Uporabljeno',
    requestTitle: 'Zahteva za',
    responseTitle: 'Odgovor iz',
  },
  setBuiltInTools: {
    info: 'Informacije',
    setting: 'Nastavitve',
    toolDescription: 'Opis orodja',
    parameters: 'parametri',
    string: 'niz',
    number: 'številka',
    required: 'Obvezno',
    infoAndSetting: 'Informacije in nastavitve',
    file: 'datoteka',
  },
  noCustomTool: {
    title: 'Ni prilagojenih orodij!',
    content: 'Tukaj lahko dodate in upravljate svoja prilagojena orodja za gradnjo AI aplikacij.',
    createTool: 'Ustvari orodje',
  },
  noSearchRes: {
    title: 'Oprostite, ni rezultatov!',
    content: 'Nismo našli nobenih orodij, ki ustrezajo vašemu iskanju.',
    reset: 'Ponastavi iskanje',
  },
  builtInPromptTitle: 'Poziv',
  toolRemoved: 'Orodje odstranjeno',
  notAuthorized: 'Orodje ni avtorizirano',
  howToGet: 'Kako pridobiti',
  openInStudio: 'Odpri v Studiju',
  toolNameUsageTip: 'Ime klica orodja za sklepanja in pozivanje agenta',
  copyToolName: 'Kopiraj ime',
  noTools: 'Orodja niso bila najdena',
  mcp: {
    create: {
      cardTitle: 'Dodaj strežnik MCP (HTTP)',
      cardLink: 'Več o integraciji strežnika MCP',
    },
    noConfigured: 'Nekonfiguriran strežnik',
    updateTime: 'Posodobljeno',
    toolsCount: '{count} orodij',
    noTools: 'Na voljo ni orodij',
    modal: {
      title: 'Dodaj strežnik MCP (HTTP)',
      editTitle: 'Uredi strežnik MCP (HTTP)',
      name: 'Ime in ikona',
      namePlaceholder: 'Poimenuj svoj strežnik MCP',
      serverUrl: 'URL strežnika',
      serverUrlPlaceholder: 'URL do končne točke strežnika',
      serverUrlWarning: 'Posodobitev naslova strežnika lahko prekine aplikacije, ki so odvisne od tega strežnika',
      serverIdentifier: 'Identifikator strežnika',
      serverIdentifierTip: 'Edinstven identifikator za strežnik MCP v delovnem prostoru. Samo male črke, številke, podčrtaji in vezaji. Največ 24 znakov.',
      serverIdentifierPlaceholder: 'Edinstven identifikator, npr. moj-mcp-streznik',
      serverIdentifierWarning: 'Strežnik po spremembi ID-ja ne bo prepoznan s strani obstoječih aplikacij',
      cancel: 'Prekliči',
      save: 'Shrani',
      confirm: 'Dodaj in avtoriziraj',
      timeout: 'Časovna omejitev',
      sseReadTimeout: 'SSE časovna omejitev branja',
    },
    delete: 'Odstrani strežnik MCP',
    deleteConfirmTitle: 'Odstraniti {mcp}?',
    operation: {
      edit: 'Uredi',
      remove: 'Odstrani',
    },
    authorize: 'Avtoriziraj',
    authorizing: 'Avtoriziranje...',
    authorizingRequired: 'Avtorizacija je zahtevana',
    authorizeTip: 'Po avtorizaciji bodo orodja prikazana tukaj.',
    update: 'Posodobi',
    updating: 'Posodabljanje...',
    gettingTools: 'Pridobivanje orodij...',
    updateTools: 'Posodabljanje orodij...',
    toolsEmpty: 'Orodja niso naložena',
    getTools: 'Pridobi orodja',
    toolUpdateConfirmTitle: 'Posodobi seznam orodij',
    toolUpdateConfirmContent: 'Posodobitev seznama orodij lahko vpliva na obstoječe aplikacije. Želite nadaljevati?',
    toolsNum: 'Vključenih {count} orodij',
    onlyTool: 'Vključeno 1 orodje',
    identifier: 'Identifikator strežnika (Kliknite za kopiranje)',
    server: {
      title: 'Strežnik MCP',
      url: 'URL strežnika',
      reGen: 'Želite ponovno ustvariti URL strežnika?',
      addDescription: 'Dodaj opis',
      edit: 'Uredi opis',
      modal: {
        addTitle: 'Dodajte opis za omogočitev strežnika MCP',
        editTitle: 'Uredi opis',
        description: 'Opis',
        descriptionPlaceholder: 'Pojasnite, kaj to orodje počne in kako naj ga uporablja LLM',
        parameters: 'Parametri',
        parametersTip: 'Dodajte opise za vsak parameter, da pomagate LLM razumeti njihov namen in omejitve.',
        parametersPlaceholder: 'Namen in omejitve parametra',
        confirm: 'Omogoči strežnik MCP',
      },
      publishTip: 'Aplikacija ni objavljena. Najprej objavite aplikacijo.',
    },
  },
}

export default translation
