const translation = {
  createApp: 'USTVARI APLIKACIJO',
  types: {
    all: 'Vse',
    chatbot: 'Klepetalnik',
    agent: 'Agent',
    workflow: 'Potek dela',
    completion: 'Do<PERSON>nje<PERSON><PERSON>',
    advanced: 'Tok klepeta',
    basic: 'Osnoven',
  },
  duplicate: 'Podvoji',
  duplicateTitle: 'Podvoji aplikacijo',
  export: 'Izvozi DSL',
  exportFailed: 'Izvoz DSL ni uspel.',
  importDSL: 'Uvozi datoteko DSL',
  createFromConfigFile: 'Ustvari iz datoteke DSL',
  importFromDSL: 'Uvozi iz DSL',
  importFromDSLFile: 'Iz datoteke DSL',
  importFromDSLUrl: 'Iz URL-ja',
  importFromDSLUrlPlaceholder: 'Tukaj prilepi povezavo DSL',
  dslUploader: {
    button: 'Povlecite in spustite datoteko, ali',
    browse: 'Prebrsk<PERSON>',
  },
  deleteAppConfirmTitle: 'I<PERSON><PERSON><PERSON><PERSON> to aplikacijo?',
  deleteAppConfirmContent:
    'Brisanje aplikacije je nepopravljivo. Uporabniki ne bodo več imeli dostopa do vaše aplikacije, vse konfiguracije in dnevniki pa bodo trajno izbrisani.',
  appDeleted: 'Aplikacija izbrisana',
  appDeleteFailed: 'Brisanje aplikacije ni uspelo',
  join: 'Pridruži se skupnosti',
  communityIntro:
    'Pogovarjajte se s člani ekipe, sodelavci in razvijalci na različnih kanalih.',
  roadmap: 'Oglejte si naš načrt',
  newApp: {
    startFromBlank: 'Ustvari iz nič',
    startFromTemplate: 'Ustvari iz predloge',
    workflowWarning: 'Trenutno v beta različici',
    captionName: 'Ikona in ime aplikacije',
    appNamePlaceholder: 'Poimenujte svojo aplikacijo',
    captionDescription: 'Opis',
    appDescriptionPlaceholder: 'Vnesite opis aplikacije',
    useTemplate: 'Uporabi to predlogo',
    previewDemo: 'Predogled demo različice',
    chatApp: 'Pomočnik',
    chatAppIntro:
      'Želim zgraditi aplikacijo, ki temelji na klepetu. Ta aplikacija uporablja format vprašanj in odgovorov, ki omogoča več krogov neprekinjenega pogovora.',
    agentAssistant: 'Novi pomočnik agenta',
    completeApp: 'Generator besedila',
    completeAppIntro:
      'Želim ustvariti aplikacijo, ki na podlagi pozivov generira visokokakovostno besedilo, kot je ustvarjanje člankov, povzetkov, prevodov in več.',
    showTemplates: 'Želim izbrati iz predloge',
    hideTemplates: 'Vrni se na izbiro načina',
    Create: 'Ustvari',
    Cancel: 'Prekliči',
    nameNotEmpty: 'Ime ne sme biti prazno',
    appTemplateNotSelected: 'Izberite predlogo',
    appTypeRequired: 'Izberite vrsto aplikacije',
    appCreated: 'Aplikacija ustvarjena',
    appCreateFailed: 'Ustvarjanje aplikacije ni uspelo',
    appCreateDSLErrorTitle: 'Nezdružljivost različice',
    caution: 'Previdnost',
    Confirm: 'Potrditi',
    appCreateDSLErrorPart1: 'Odkrita je bila pomembna razlika v različicah DSL. Vsiljevanje uvoza lahko povzroči nepravilno delovanje aplikacije.',
    appCreateDSLErrorPart3: 'Trenutna različica aplikacije DSL:',
    appCreateDSLErrorPart4: 'Sistemsko podprta različica DSL:',
    appCreateDSLWarning: 'Pozor: Razlika v različici DSL lahko vpliva na nekatere funkcije',
    appCreateDSLErrorPart2: 'Želite nadaljevati?',
    advancedShortDescription: 'Potek dela izboljšan za večkratne pogovore',
    noAppsFound: 'Ni bilo najdenih aplikacij',
    agentShortDescription: 'Inteligentni agent z razmišljanjem in avtonomno uporabo orodij',
    foundResult: '{{count}} Rezultat',
    foundResults: '{{count}} Rezultati',
    noTemplateFoundTip: 'Poskusite iskati z različnimi ključnimi besedami.',
    optional: 'Neobvezno',
    forBeginners: 'Bolj osnovne vrste aplikacij',
    forAdvanced: 'ZA NAPREDNE UPORABNIKE',
    noIdeaTip: 'Nimate idej? Oglejte si naše predloge',
    agentUserDescription: 'Inteligentni agent, ki je sposoben iterativnega sklepanja in avtonomne uporabe orodij za doseganje ciljev nalog.',
    completionShortDescription: 'Pomočnik AI za naloge generiranja besedila',
    chatbotUserDescription: 'Hitro zgradite chatbota, ki temelji na LLM, s preprosto konfiguracijo. Na Chatflow lahko preklopite pozneje.',
    completionUserDescription: 'Hitro ustvarite pomočnika AI za naloge ustvarjanja besedila s preprosto konfiguracijo.',
    advancedUserDescription: 'Potek dela z dodatnimi funkcijami spomina in vmesnikom za klepetanje.',
    workflowUserDescription: 'Vizualno ustvarjajte avtonomne AI poteke s preprostim vlečenjem in spuščanjem.',
    noTemplateFound: 'Predloge niso bile najdene',
    workflowShortDescription: 'Agentni tok za inteligentne avtomatizacije',
    chatbotShortDescription: 'Chatbot, ki temelji na LLM, s preprosto nastavitvijo',
    chooseAppType: 'Izberite vrsto aplikacije',
    learnMore: 'Izvedi več',
    dropDSLToCreateApp: 'Spustite DSL datoteko sem, da ustvarite aplikacijo',
  },
  editApp: 'Uredi informacije',
  editAppTitle: 'Uredi informacije o aplikaciji',
  editDone: 'Informacije o aplikaciji posodobljene',
  editFailed: 'Posodobitev informacij o aplikaciji ni uspela',
  iconPicker: {
    ok: 'V redu',
    cancel: 'Prekliči',
    emoji: 'Emoji',
    image: 'Slika',
  },
  answerIcon: {
    title: 'Uporabite ikono web app za zamenjavo 🤖',
    description: 'Ali uporabiti ikono web app za zamenjavo 🤖 v deljeni aplikaciji',
    descriptionInExplore: 'Ali uporabiti ikono web app za zamenjavo 🤖 v razdelku Razišči',
  },
  switch: 'Preklopi na Workflow Orchestrate',
  switchTipStart: 'Za vas bo ustvarjena nova kopija aplikacije, ki bo preklopila na Workflow Orchestrate. Nova kopija ne bo ',
  switchTip: 'dovolila',
  switchTipEnd: ' preklopa nazaj na Basic Orchestrate.',
  switchLabel: 'Kopija aplikacije, ki bo ustvarjena',
  removeOriginal: 'Izbriši izvirno aplikacijo',
  switchStart: 'Začni preklop',
  typeSelector: {
    all: 'VSE VRSTE',
    chatbot: 'Klepetalnik',
    agent: 'Agent',
    workflow: 'Potek dela',
    completion: 'Dopolnjevanje',
    advanced: 'Tok klepeta',
  },
  tracing: {
    title: 'Sledenje uspešnosti aplikacije',
    description: 'Konfiguracija ponudnika LLMOps tretje osebe in sledenje uspešnosti aplikacije.',
    config: 'Konfiguracija',
    view: 'Ogled',
    collapse: 'Strni',
    expand: 'Razširi',
    tracing: 'Sledenje',
    disabled: 'Onemogočeno',
    disabledTip: 'Najprej konfigurirajte ponudnika',
    enabled: 'V storitvi',
    tracingDescription: 'Zajem celotnega konteksta izvajanja aplikacije, vključno s klici LLM, kontekstom, pozivi, zahtevami HTTP in še več, na platformo za sledenje tretje osebe.',
    configProviderTitle: {
      configured: 'Konfigurirano',
      notConfigured: 'Konfigurirajte ponudnika za omogočanje sledenja',
      moreProvider: 'Več ponudnikov',
    },
    arize: {
      title: 'Arize',
      description: 'Podjetniško opazovanje LLM, spletno in nespletno vrednotenje, nadzorovanje in eksperimentiranje — s podporo OpenTelemetry. Namenjeno aplikacijam, ki temeljijo na LLM in agentih.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Odprtokodna in na OpenTelemetry osnovana platforma za opazovanje, vrednotenje, inženiring pozivov ter eksperimentiranje za vaše LLM poteke dela in agente.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Vse-v-enem razvijalska platforma za vsak korak življenjskega cikla aplikacije, ki jo poganja LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Sledi, vrednoti, upravlja pozive in meri za odpravljanje napak in izboljšanje vaše aplikacije LLM.',
    },
    inUse: 'V uporabi',
    configProvider: {
      title: 'Konfiguracija',
      placeholder: 'Vnesite vaš {{key}}',
      project: 'Projekt',
      publicKey: 'Javni ključ',
      secretKey: 'Skrivni ključ',
      viewDocsLink: 'Ogled dokumentov {{key}}',
      removeConfirmTitle: 'Odstraniti konfiguracijo {{key}}?',
      removeConfirmContent: 'Trenutna konfiguracija je v uporabi, odstranitev bo onemogočila funkcijo sledenja.',
    },
    opik: {
      description: 'Opik je odprtokodna platforma za ocenjevanje, testiranje in spremljanje aplikacij LLM.',
      title: 'Opik',
    },
    weave: {
      title: 'Tkanje',
      description: 'Weave je odprtokodna platforma za vrednotenje, testiranje in spremljanje aplikacij LLM.',
    },
    aliyun: {
      title: 'Oblačni nadzor',
      description: 'Popolnoma upravljana in brez vzdrževanja platforma za opazovanje, ki jo zagotavlja Alibaba Cloud, omogoča takojšnje spremljanje, sledenje in ocenjevanje aplikacij Dify.',
    },
  },
  mermaid: {
    handDrawn: 'Ročno narisano',
    classic: 'Klasičen',
  },
  openInExplore: 'Odpri v razišči',
  newAppFromTemplate: {
    sidebar: {
      Programming: 'Programiranje',
      Recommended: 'Priporočljivo',
      Writing: 'Pisanje',
      Assistant: 'Pomočnik',
      Workflow: 'Potek dela',
      HR: 'HR',
      Agent: 'Agent',
    },
    byCategories: 'PO KATEGORIJAH',
    searchAllTemplate: 'Preišči vse predloge ...',
  },
  showMyCreatedAppsOnly: 'Prikaži samo aplikacije, ki sem jih ustvaril',
  appSelector: {
    params: 'PARAMETRI APLIKACIJE',
    noParams: 'Parametri niso potrebni',
    label: 'APL',
    placeholder: 'Izberite aplikacijo ...',
  },
  structOutput: {
    configure: 'Konfiguriraj',
    structured: 'Strukturirano',
    modelNotSupported: 'Model ni podprt',
    required: 'Zahtevano',
    moreFillTip: 'Prikazovanje največ 10 ravni gnezdenja',
    LLMResponse: 'LLM odziv',
    notConfiguredTip: 'Strukturiranega izhoda še ni mogoče konfigurirati',
    modelNotSupportedTip: 'Trenutni model ne podpira te funkcije in se samodejno zniža na vbrizgavanje pozivov.',
    structuredTip: 'Strukturirani izhodi so funkcija, ki zagotavlja, da bo model vedno generiral odgovore, ki se držijo vašega posredovanega JSON sheme.',
  },
  accessItemsDescription: {
    anyone: 'Vsakdo lahko dostopa do spletne aplikacije',
    specific: 'Samo določenim skupinam ali članom je omogočen dostop do spletne aplikacije',
    organization: 'Vsakdo v organizaciji lahko dostopa do spletne aplikacije',
    external: 'Samo avtentificirani zunanji uporabniki lahko dostopajo do spletne aplikacije.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Kdorkoli s povezavo',
      specific: 'Specifične skupine ali člani',
      organization: 'Samo člani znotraj podjetja',
      external: 'Avtorizirani zunanji uporabniki',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Išči skupine in člane',
      allMembers: 'Vsi člani',
      expand: 'Razširi',
      noResult: 'Brez rezultata',
    },
    title: 'Nadzor dostopa do spletne aplikacije',
    description: 'Nastavite dovoljenja za dostop do spletne aplikacije',
    accessLabel: 'Kdo ima dostop',
    groups_one: '{{count}} SKUPINA',
    groups_other: '{{count}} SKUPIN',
    members_one: '{{count}} ČLAN',
    members_other: '{{count}} ČLANOV',
    updateSuccess: 'Posodobitev uspešna',
    noGroupsOrMembers: 'Nobene skupine ali članov ni izbranih',
    webAppSSONotEnabledTip: 'Prosimo, da se obrnete na skrbnika podjetja, da konfigurira način avtentikacije spletne aplikacije.',
  },
  publishApp: {
    title: 'Kdo lahko dostopa do spletne aplikacije',
    notSet: 'Ni nastavljeno',
    notSetDesc: 'Trenutno nihče ne more dostopati do spletne aplikacije. Prosimo, nastavite dovoljenja.',
  },
  accessControl: 'Nadzor dostopa do spletne aplikacije',
  noAccessPermission: 'Brez dovoljenja za dostop do spletne aplikacije',
  maxActiveRequestsPlaceholder: 'Vnesite 0 za neomejeno',
  maxActiveRequests: 'Maksimalno število hkratnih zahtevkov',
  maxActiveRequestsTip: 'Največje število hkrati aktivnih zahtevkov na aplikacijo (0 za neomejeno)',
  gotoAnything: {
    actions: {
      searchWorkflowNodes: 'Iskanje vozlišč poteka dela',
      searchKnowledgeBasesDesc: 'Iskanje in krmarjenje do zbirk znanja',
      searchWorkflowNodesHelp: 'Ta funkcija deluje le pri ogledu poteka dela. Najprej se pomaknite do poteka dela.',
      searchApplicationsDesc: 'Iskanje in krmarjenje do aplikacij',
      searchPlugins: 'Iskanje vtičnikov',
      searchApplications: 'Iskanje aplikacij',
      searchWorkflowNodesDesc: 'Iskanje vozlišč in skok nanje v trenutnem poteku dela po imenu ali vrsti',
      searchKnowledgeBases: 'Iskanje po zbirkah znanja',
      searchPluginsDesc: 'Iskanje in krmarjenje do vtičnikov',
      themeCategoryTitle: 'Tema',
      themeLight: 'Svetla tematika',
      runTitle: 'Ukazi',
      themeSystem: 'Sistem tema',
      themeDarkDesc: 'Uporabite temen način',
      themeLightDesc: 'Uporabite svetlo prikazovanje',
      themeCategoryDesc: 'Preklopi temo aplikacije',
      themeDark: 'Temna tema',
      languageCategoryDesc: 'Preklopi jezik vmesnika',
      languageCategoryTitle: 'Jezik',
      themeSystemDesc: 'Sledite videzu svojega operacijskega sistema',
      runDesc: 'Zaženi hitre ukaze (teme, jezik, ...)',
      languageChangeDesc: 'Spremeni jezik vmesnika',
      slashDesc: 'Izvedi ukaze kot so /tema, /jezik',
    },
    emptyState: {
      noPluginsFound: 'Vtičnikov ni mogoče najti',
      noWorkflowNodesFound: 'Vozlišča poteka dela niso bila najdena',
      noKnowledgeBasesFound: 'Zbirk znanja ni mogoče najti',
      noAppsFound: 'Ni bilo najdenih aplikacij',
      tryDifferentTerm: 'Poskusite z drugim iskalnim izrazom ali odstranite filter {{mode}}',
      trySpecificSearch: 'Poskusite {{shortcuts}} za specifična iskanja',
    },
    groups: {
      workflowNodes: 'Vozlišča poteka dela',
      apps: 'Aplikacije',
      knowledgeBases: 'Baze znanja',
      plugins: 'Vtičniki',
      commands: 'Ukazi',
    },
    searching: 'Iskanje...',
    searchTitle: 'Poiščite karkoli',
    searchTemporarilyUnavailable: 'Iskanje začasno ni na voljo',
    someServicesUnavailable: 'Nekatere iskalne storitve niso na voljo',
    noResults: 'Ni najdenih rezultatov',
    clearToSearchAll: 'Počisti @ za iskanje vseh',
    searchPlaceholder: 'Poiščite ali vnesite @ za ukaze ...',
    searchFailed: 'Iskanje ni uspelo',
    useAtForSpecific: 'Uporaba znaka @ za določene vrste',
    servicesUnavailableMessage: 'Pri nekaterih iskalnih storitvah se morda pojavljajo težave. Poskusite znova čez trenutek.',
    commandHint: 'Vnesite @ za brskanje po kategoriji',
    selectSearchType: 'Izberite, kaj želite iskati',
    searchHint: 'Začnite tipkati, da takoj preiščete vse',
    resultCount: '{{count}} rezultat',
    resultCount_other: '{{count}} rezultatov',
    inScope: 'v {{scope}}s',
    tryDifferentSearch: 'Poskusite uporabiti drug iskalni izraz',
    noMatchingCommands: 'Ujemajoči se ukazi niso našli',
  },
}

export default translation
