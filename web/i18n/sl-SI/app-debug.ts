const translation = {
  pageTitle: {
    line1: 'PROMPT',
    line2: 'Inženiring',
  },
  orchestrate: '<PERSON><PERSON><PERSON><PERSON>',
  promptMode: {
    simple: 'Preklopi na strokovni način, da ure<PERSON><PERSON> celoten PROMPT',
    advanced: '<PERSON><PERSON><PERSON><PERSON> način',
    switchBack: 'Preklopi nazaj',
    advancedWarning: {
      title: 'Preklopil si na strokovni način. Ko spremeniš PROMPT, ne moreš več preklopiti nazaj v osnovni način.',
      description: 'V strokovnem načinu lahko urejaš celoten PROMPT.',
      learnMore: 'Preberi več',
      ok: 'V redu',
    },
    operation: {
      addMessage: 'Dodaj sporočilo',
    },
    contextMissing: 'Manjka komponenta konteksta, zato učinkovitost PROMPT-a morda ne bo najboljša.',
  },
  operation: {
    applyConfig: 'Objavi',
    resetConfig: 'Ponastavi',
    debugConfig: 'Odpravl<PERSON><PERSON> napak',
    addFeature: '<PERSON>da<PERSON> funkcional<PERSON>t',
    automatic: '<PERSON><PERSON><PERSON>',
    stopResponding: 'Prenehaj odgovarjati',
    agree: 'vše<PERSON>',
    disagree: 'ni všeč',
    cancelAgree: 'Prekliči všeček',
    cancelDisagree: 'Prekliči nevšečnost',
    userAction: 'Uporabnik ',
  },
  notSetAPIKey: {
    title: 'Ključ ponudnika LLM ni nastavljen',
    trailFinished: 'Preizkus končan',
    description: 'Ključ ponudnika LLM ni nastavljen. Pred odpravljanjem napak je treba nastaviti ključ.',
    settingBtn: 'Pojdi v nastavitve',
  },
  trailUseGPT4Info: {
    title: 'GPT-4 trenutno ni podprt',
    description: 'Za uporabo GPT-4 je treba nastaviti API ključ.',
  },
  feature: {
    groupChat: {
      title: 'Izboljšanje klepeta',
      description: 'Dodajanje prednastavitev klepeta lahko izboljša uporabniško izkušnjo.',
    },
    groupExperience: {
      title: 'Izboljšanje izkušnje',
    },
    conversationOpener: {
      title: 'Začetek pogovora',
      description: 'V klepetu AI običajno začne pogovor z uporabnikom z dobrodošlico.',
    },
    suggestedQuestionsAfterAnswer: {
      title: 'Nadaljnja vprašanja',
      description: 'Nastavitev predlogov za naslednja vprašanja lahko uporabnikom izboljša klepet.',
      resDes: '3 predlogi za naslednje vprašanje uporabnika.',
      tryToAsk: 'Poskusi vprašati',
    },
    moreLikeThis: {
      title: 'Več takšnih',
      description: 'Ustvari več besedil naenkrat, nato pa jih urejaj in nadaljuj z ustvarjanjem.',
      generateNumTip: 'Število generacij vsakič',
      tip: 'Uporaba te funkcije povzroča dodatno porabo žetonov.',
    },
    speechToText: {
      title: 'Govor v besedilo',
      description: 'Ko je omogočeno, lahko uporabljaš glasovni vnos.',
      resDes: 'Glasovni vnos je omogočen.',
    },
    textToSpeech: {
      title: 'Besedilo v govor',
      description: 'Ko je omogočeno, lahko besedilo pretvoriš v govor.',
      resDes: 'Pretvorba besedila v zvok je omogočena.',
    },
    citation: {
      title: 'Citati in pripisovanja',
      description: 'Ko je omogočeno, prikaži izvorni dokument in pripisani del generirane vsebine.',
      resDes: 'Citati in pripisovanja so omogočeni.',
    },
    annotation: {
      title: 'Odgovor z opombami',
      description: 'Ročno lahko dodate visokokakovostne odgovore v predpomnilnik za prednostno ujemanje s podobnimi vprašanji uporabnikov.',
      resDes: 'Odgovor z opombami je omogočen.',
      scoreThreshold: {
        title: 'Prag ujemanja',
        description: 'Uporabljeno za nastavitev praga podobnosti za odgovor z opombami.',
        easyMatch: 'Lahko ujemanje',
        accurateMatch: 'Natančno ujemanje',
      },
      matchVariable: {
        title: 'Spremenljivka za ujemanje',
        choosePlaceholder: 'Izberi spremenljivko za ujemanje',
      },
      cacheManagement: 'Upravljanje opomb',
      cached: 'Z opombo',
      remove: 'Odstrani',
      removeConfirm: 'Izbrisati to opombo?',
      add: 'Dodaj opombo',
      edit: 'Uredi opombo',
    },
    dataSet: {
      title: 'Kontekst',
      noData: 'Uvozi znanje kot kontekst',
      words: 'Besede',
      textBlocks: 'Bloki besedila',
      selectTitle: 'Izberi referenčno znanje',
      selected: 'Izbrano znanje',
      noDataSet: 'Znanje ni bilo najdeno',
      toCreate: 'Pojdi na ustvarjanje',
      notSupportSelectMulti: 'Trenutno je podprto le eno znanje',
      queryVariable: {
        title: 'Spremenljivka poizvedbe',
        tip: 'Ta spremenljivka bo uporabljena kot vnos poizvedbe za pridobitev kontekstnih informacij.',
        choosePlaceholder: 'Izberi spremenljivko poizvedbe',
        noVar: 'Ni spremenljivk',
        noVarTip: 'ustvari spremenljivko v razdelku Spremenljivke',
        unableToQueryDataSet: 'Neuspešna poizvedba po znanju',
        unableToQueryDataSetTip: 'Neuspešna poizvedba po znanju, izberi spremenljivko poizvedbe v razdelku kontekst.',
        ok: 'V redu',
        contextVarNotEmpty: 'Spremenljivka poizvedbe ne sme biti prazna',
        deleteContextVarTitle: 'Izbrisati spremenljivko “{{varName}}”?',
        deleteContextVarTip: 'Ta spremenljivka je nastavljena kot spremenljivka za poizvedbo po kontekstu. Če jo odstraniš, bo to vplivalo na uporabo znanja. Če jo želiš izbrisati, ponovno izberi v razdelku kontekst.',
      },
    },
    tools: {
      title: 'Orodja',
      tips: 'Orodja nudijo standardiziran način klicanja API-jev, pri čemer se uporabniški vnos ali spremenljivke uporabijo kot parametri za poizvedovanje zunanjih podatkov.',
      toolsInUse: '{{count}} orodij v uporabi',
      modal: {
        title: 'Orodje',
        toolType: {
          title: 'Tip orodja',
          placeholder: 'Izberi tip orodja',
        },
        name: {
          title: 'Ime',
          placeholder: 'Vnesi ime',
        },
        variableName: {
          title: 'Ime spremenljivke',
          placeholder: 'Vnesi ime spremenljivke',
        },
      },
    },
    conversationHistory: {
      title: 'Zgodovina pogovorov',
      description: 'Nastavi predpone imen za vloge v pogovoru',
      tip: 'Zgodovina pogovorov ni omogočena. Dodaj <histories> v zgornji PROMPT.',
      learnMore: 'Preberi več',
      editModal: {
        title: 'Uredi imena vlog v pogovoru',
        userPrefix: 'Predpona uporabnika',
        assistantPrefix: 'Predpona pomočnika',
      },
    },
    toolbox: {
      title: 'ORODJA',
    },
    moderation: {
      title: 'Moderiranje vsebine',
      description: 'Zagotovi varno izhodno vsebino s pomočjo API-ja za moderiranje ali vzdrževanja seznama občutljivih besed.',
      allEnabled: 'VSEBINA VNOSA/IZHODA omogočena',
      inputEnabled: 'VSEBINA VNOSA omogočena',
      outputEnabled: 'VSEBINA IZHODA omogočena',
      modal: {
        title: 'Nastavitve moderiranja vsebine',
        provider: {
          title: 'Ponudnik',
          openai: 'OpenAI Moderiranje',
          openaiTip: {
            prefix: 'OpenAI Moderiranje zahteva nastavljen API ključ pri ',
            suffix: '.',
          },
          keywords: 'Ključne besede',
        },
        keywords: {
          tip: 'Vsaka beseda na lastni vrstici, ločena z vrsticami. Največ 100 znakov na vrstico.',
          placeholder: 'Vsaka beseda na lastni vrstici, ločena z vrsticami',
          line: 'Vrstica',
        },
        content: {
          input: 'Moderiraj VSEBINO VNOSA',
          output: 'Moderiraj VSEBINO IZHODA',
          preset: 'Prednastavljeni odgovori',
          errorMessage: 'Prednastavljeni odgovori ne smejo biti prazni',
          condition: 'Zmerna vsebina INPUT in OUTPUT je omogočena vsaj ena',
          supportMarkdown: 'Podprt za Markdown',
          fromApi: 'Prednastavljene odgovore vrne API',
          placeholder: 'Prednastavljena vsebina odgovorov tukaj',
        },
        openaiNotConfig: {
          after: '',
          before: 'Za moderiranje OpenAI potrebujete ključ OpenAI API, konfiguriran v',
        },
      },
      contentEnableLabel: 'Moderiranje vsebine omogočeno',
    },
    debug: {
      title: 'Odpravljanje napak',
      description: 'Debugiranje omogoča pregled podrobnih informacij, kot so podatki API-jev, vklop dnevnikov, opozorila in še več.',
    },
    agent: {
      title: 'Pomočnik',
      description: 'Osnovne informacije in odgovorne naloge pomočnika.',
      prompts: 'Temeljni PROMPT',
      message: {
        title: 'Vrstice sporočila',
        user: 'Uporabnik',
        assistant: 'Pomočnik',
      },
    },
    history: {
      title: 'Zgodovina',
      notFound: 'Zgodovina ni bila najdena',
      notOpen: 'Zgodovina ni odprta',
    },
    prompt: {
      title: 'Vsebina PROMPT-a',
    },
    message: {
      title: 'Sporočilo',
      description: 'Način nastavitve formatiranega pogovora.',
      tryChat: 'Preizkusi klepet',
    },
    theme: {
      title: 'Tema',
      themes: {
        default: 'Osnovna tema',
        light: 'Svetla tema',
        dark: 'Temna tema',
        custom: 'Prilagodi temo',
      },
      modal: {
        title: 'Nastavitve teme',
        primaryColor: {
          title: 'Primarna barva',
          placeholder: 'Izberi primarno barvo',
        },
        textColor: {
          title: 'Barva besedila',
          placeholder: 'Izberi barvo besedila',
        },
        ok: 'V redu',
      },
    },
    fileUpload: {
      title: 'Nalaganje datoteke',
      description: 'Pogovorno polje omogoča nalaganje slik, dokumentov in drugih datotek.',
      supportedTypes: 'Podprte vrste datotek',
      numberLimit: 'Največje število nalaganj',
      modalTitle: 'Nastavitve nalaganja datoteke',
    },
    imageUpload: {
      title: 'Nalaganje slike',
      description: 'Omogoči nalaganje slik.',
      supportedTypes: 'Podprte vrste datotek',
      numberLimit: 'Največje število nalaganj',
      modalTitle: 'Nastavitve nalaganja slike',
    },
    bar: {
      empty: 'Omogoči funkcije za izboljšanje uporabniške izkušnje spletne aplikacije',
      enableText: 'Funkcije omogočene',
      manage: 'Upravljaj',
    },
    documentUpload: {
      title: 'Dokument',
      description: 'Omogočitev dokumenta bo omogočila modelu, da sprejme dokumente in odgovori na vprašanja o njih.',
    },
    audioUpload: {
      title: 'Zvok',
      description: 'Omogočitev zvoka bo omogočila modelu, da obdela zvočne datoteke za prepisovanje in analizo.',
    },
  },
  codegen: {
    instruction: 'Navodila',
    title: 'Generator kode',
    resTitle: 'Ustvarjena koda',
    loading: 'Generiranje kode ...',
    generatedCodeTitle: 'Ustvarjena koda',
    noDataLine1: 'Na levi opišite primer uporabe,',
    noDataLine2: 'Predogled kode bo prikazan tukaj.',
    instructionPlaceholder: 'Vnesite podroben opis kode, ki jo želite ustvariti.',
    apply: 'Uporabiti',
    generate: 'Ustvariti',
    overwriteConfirmTitle: 'Prepisati obstoječo kodo?',
    applyChanges: 'Uporaba sprememb',
    overwriteConfirmMessage: 'S tem dejanjem boste prepisali obstoječo kodo. Želite nadaljevati?',
    description: 'Generator kode uporablja konfigurirane modele za ustvarjanje visokokakovostne kode na podlagi vaših navodil. Navedite jasna in podrobna navodila.',
  },
  generate: {
    template: {
      pythonDebugger: {
        name: 'Python razhroščevalnik',
        instruction: 'Bot, ki lahko ustvari in razhrošči vašo kodo na podlagi vaših navodil',
      },
      translation: {
        name: 'Prevod',
        instruction: 'Prevajalec, ki zna prevesti več jezikov',
      },
      professionalAnalyst: {
        name: 'Strokovni analitik',
        instruction: 'Pridobite vpoglede, prepoznajte tveganja in destilirajte ključne informacije iz dolgih poročil v en sam zapisek',
      },
      excelFormulaExpert: {
        instruction: 'Chatbot, ki lahko začetnikom pomaga razumeti, uporabljati in ustvarjati Excelove formule na podlagi uporabniških navodil',
        name: 'Strokovnjak za formule v Excelu',
      },
      travelPlanning: {
        instruction: 'Pomočnik za načrtovanje potovanj je inteligentno orodje, ki uporabnikom pomaga pri enostavnem načrtovanju potovanj',
        name: 'Načrtovanje potovanj',
      },
      SQLSorcerer: {
        name: 'Čarovnik SQL',
        instruction: 'Pretvorba vsakdanjega jezika v poizvedbe SQL',
      },
      GitGud: {
        instruction: 'Ustvarite ustrezne ukaze Git na podlagi dejanj nadzora različic, ki jih je opisal uporabnik',
        name: 'Git gud',
      },
      meetingTakeaways: {
        name: 'Povzetki s srečanja',
        instruction: 'Srečanja destilirajte v jedrnate povzetke, vključno s temami za razpravo, ključnimi ugotovitvami in dejanji',
      },
      writingsPolisher: {
        name: 'Pisanje polir',
        instruction: 'Uporabite napredne tehnike urejanja besedil za izboljšanje svojega pisanja',
      },
    },
    apply: 'Uporabiti',
    generate: 'Ustvariti',
    instructionPlaceHolder: 'Napišite jasna in specifična navodila.',
    resTitle: 'Ustvarjen poziv',
    noDataLine2: 'Predogled orkestracije bo prikazan tukaj.',
    overwriteMessage: 'Če uporabite ta poziv, boste preglasili obstoječo konfiguracijo.',
    overwriteTitle: 'Preglasiti obstoječo konfiguracijo?',
    instruction: 'Navodila',
    loading: 'Orkestriranje aplikacije za vas ...',
    noDataLine1: 'Na levi opišite primer uporabe,',
    title: 'Generator pozivov',
    tryIt: 'Poskusite',
    description: 'Generator pozivov uporablja konfiguriran model za optimizacijo pozivov za višjo kakovost in boljšo strukturo. Prosimo, napišite jasna in podrobna navodila.',
    optional: 'Opcijsko',
    press: 'Pritisnite',
    dismiss: 'Odpusti',
    latest: 'Najnovejši',
    version: 'Različica',
    versions: 'Različice',
    instructionPlaceHolderLine3: 'Ton je pretrd, prosim, naredite ga bolj prijaznega.',
    insertContext: 'vstavite kontekst',
    optimizationNote: 'Opomba o optimizaciji',
    idealOutput: 'Idealni izhod',
    to: 'do',
    optimizePromptTooltip: 'Optimizirajte v generatorju pozivov',
    instructionPlaceHolderLine1: 'Naredite izhod bolj jedrnat, ohranite ključne točke.',
    instructionPlaceHolderLine2: 'Format izhoda je napačen, prosimo, strogo upoštevajte JSON format.',
    idealOutputPlaceholder: 'Opišite svoj idealen format odgovora, dolžino, ton in zahteve glede vsebine...',
    codeGenInstructionPlaceHolderLine: 'Bolj kot so povratne informacije podrobne, na primer vrste podatkov vhodov in izhodov ter način obdelave spremenljivk, natančnejše bo generiranje kode.',
    newNoDataLine1: 'V levem stolpcu vpišite navodilo in kliknite na Generiraj, da vidite odgovor.',
    instructionPlaceHolderTitle: 'Opišite, kako bi želeli izboljšati to navodilo. Na primer:',
  },
  resetConfig: {
    title: 'Potrdite ponastavitev?',
    message: 'Ponastavitev zavrže spremembe in obnovi zadnjo objavljeno konfiguracijo.',
  },
  errorMessage: {
    notSelectModel: 'Prosimo, izberite model',
    waitForImgUpload: 'Prosimo, počakajte, da se slika naloži',
    waitForResponse: 'Počakajte, da se odgovor na prejšnje sporočilo dokonča.',
    waitForBatchResponse: 'Počakajte, da se konča odgovor na paketno nalogo.',
    queryRequired: 'Besedilo zahteve je obvezno.',
    waitForFileUpload: 'Prosimo, počakajte, da se datoteka/datoteke naložijo',
  },
  warningMessage: {
    timeoutExceeded: 'Rezultati niso prikazani zaradi časovne omejitve. Prosimo, glejte dnevnike, da zberete popolne rezultate.',
  },
  variableTable: {
    action: 'Dejanja',
    optional: 'Neobvezno',
    typeString: 'Niz',
    typeSelect: 'Izbrati',
    type: 'Vrsta vnosa',
    key: 'Spremenljivi ključ',
    name: 'Ime uporabniškega vnosnega polja',
  },
  varKeyError: {},
  otherError: {
    promptNoBeEmpty: 'Poziv ne more biti prazen',
    historyNoBeEmpty: 'Zgodovina pogovorov mora biti nastavljena v pozivu',
    queryNoBeEmpty: 'Poizvedba mora biti nastavljena v pozivu',
  },
  variableConfig: {
    'file': {
      image: {
        name: 'Podoba',
      },
      audio: {
        name: 'Avdio',
      },
      document: {
        name: 'Dokument',
      },
      video: {
        name: 'Video',
      },
      custom: {
        description: 'Določite druge vrste datotek.',
        name: 'Druge vrste datotek',
        createPlaceholder: '  Pripona datoteke, npr. .doc',
      },
      supportFileTypes: 'Podporne vrste datotek',
    },
    'errorMsg': {
      varNameCanBeRepeat: 'Imena spremenljivke ni mogoče ponoviti',
      atLeastOneOption: 'Potrebna je vsaj ena možnost',
      optionRepeat: 'Ima možnosti ponavljanja',
      labelNameRequired: 'Ime nalepke je obvezno',
    },
    'content': 'Vsebina',
    'number': 'Številka',
    'selectDefaultValue': 'Izbira privzete vrednosti',
    'maxNumberOfUploads': 'Največje število nalaganj',
    'localUpload': 'Lokalno nalaganje',
    'string': 'Kratko besedilo',
    'paragraph': 'Odstavek',
    'maxLength': 'Največja dolžina',
    'defaultValue': 'Privzeta vrednost',
    'apiBasedVar': 'Spremenljivka, ki temelji na API-ju',
    'stringTitle': 'Možnosti polja z besedilom obrazca',
    'varName': 'Ime spremenljivke',
    'text-input': 'Kratko besedilo',
    'uploadFileTypes': 'Nalaganje vrst datotek',
    'noDefaultValue': 'Ni privzete vrednosti',
    'addOption': 'Dodaj možnost',
    'select': 'Izbrati',
    'hide': 'Skriti',
    'both': 'Oba',
    'multi-files': 'Seznam datotek',
    'single-file': 'Ena datoteka',
    'options': 'Možnosti',
    'addModalTitle': 'Dodajanje vhodnega polja',
    'inputPlaceholder': 'Prosimo, vnesite',
    'fieldType': 'Vrsta polja',
    'editModalTitle': 'Uredi vnosno polje',
    'required': 'Zahteva',
    'labelName': 'Ime nalepke',
  },
  vision: {
    visionSettings: {
      resolution: 'Resolucija',
      uploadMethod: 'Način nalaganja',
      high: 'Visok',
      url: 'Spletni naslov',
      localUpload: 'Lokalno nalaganje',
      uploadLimit: 'Omejitev nalaganja',
      title: 'Nastavitve vida',
      both: 'Oba',
      low: 'Nizek',
    },
    name: 'Vid',
    settings: 'Nastavitve',
    description: 'Omogoči vid bo modelu omogočil, da posname slike in odgovarja na vprašanja o njih.',
    onlySupportVisionModelTip: 'Podpira samo modele vida',
  },
  voice: {
    voiceSettings: {
      voice: 'Glas',
      language: 'Jezik',
      autoPlayDisabled: 'Off',
      autoPlayEnabled: 'Na',
      resolutionTooltip: 'Jezik glasovne podpore za pretvorbo besedila v govor。',
      title: 'Glasovne nastavitve',
      autoPlay: 'Samodejno predvajanje',
    },
    defaultDisplay: 'Privzeti glas',
    name: 'Glas',
    settings: 'Nastavitve',
    description: 'Glasovne nastavitve za pretvorbo besedila v govor',
  },
  openingStatement: {
    openingQuestion: 'Uvodna vprašanja',
    title: 'Odpiralec pogovorov',
    tooShort: 'Za ustvarjanje uvodnih pripomb za pogovor je potrebnih vsaj 20 besed začetnega poziva.',
    noDataPlaceHolder: 'Začetek pogovora z uporabnikom lahko AI pomaga vzpostaviti tesnejšo povezavo z njimi v pogovornih aplikacijah.',
    add: 'Dodati',
    writeOpener: 'Odpiralnik za urejanje',
  },
  modelConfig: {
    modeType: {
      chat: 'Chat',
      completion: 'Dokončati',
    },
    title: 'Model in parametri',
    model: 'Model',
    setTone: 'Nastavitev tona odzivov',
  },
  inputs: {
    queryPlaceholder: 'Prosimo, vnesite besedilo zahteve.',
    title: 'Odpravljanje napak in predogled',
    chatVarTip: 'Izpolnite vrednost spremenljivke, ki bo samodejno nadomeščena v pozivni besedi vsakič, ko se začne nova seja',
    queryTitle: 'Vsebina poizvedbe',
    userInputField: 'Uporabniško polje za vnos',
    run: 'TEČI',
    noPrompt: 'Poskusite napisati nekaj poziva v vnos pred pozivom',
    previewTitle: 'Takojšen predogled',
    noVar: 'Izpolnite vrednost spremenljivke, ki bo samodejno nadomeščena v pozivni besedi vsakič, ko se začne nova seja.',
    completionVarTip: 'Izpolnite vrednost spremenljivke, ki bo samodejno nadomeščena v pozivnih besedah vsakič, ko boste oddali vprašanje.',
  },
  datasetConfig: {
    retrieveOneWay: {
      title: 'Pridobivanje N-na-1',
      description: 'Na podlagi namena uporabnika in opisov znanja agent avtonomno izbere najboljše znanje za poizvedovanje. Najboljše za aplikacije z izrazitim, omejenim znanjem.',
    },
    retrieveMultiWay: {
      title: 'Pridobivanje več poti',
      description: 'Na podlagi namena uporabnika poizvedbe v celotnem znanju, pridobijo ustrezno besedilo iz več virov in izberejo najboljše rezultate, ki se ujemajo z uporabniško poizvedbo po ponovnem razvrščanju.',
    },
    params: 'Params',
    embeddingModelRequired: 'Potreben je konfiguriran model vdelave',
    settingTitle: 'Nastavitve pridobivanja',
    rerankModelRequired: 'Potreben je konfiguriran model ponovnega razvrščanja',
    knowledgeTip: 'Kliknite gumb " " za dodajanje znanja',
    score_threshold: 'Prag ocenjevanja',
    score_thresholdTip: 'Uporablja se za nastavitev praga podobnosti za filtriranje kosov.',
    retrieveChangeTip: 'Spreminjanje kazalnega načina in načina pridobivanja lahko vpliva na aplikacije, povezane s tem znanjem.',
    top_k: 'Vrh K',
    top_kTip: 'Uporablja se za filtriranje kosov, ki so najbolj podobni vprašanjem uporabnikov. Sistem bo tudi dinamično prilagajal vrednost Top K, glede na max_tokens izbranega modela.',
  },
  assistantType: {
    chatAssistant: {
      name: 'Osnovni pomočnik',
      description: 'Ustvarjanje pomočnika za klepet z uporabo velikega jezikovnega modela',
    },
    agentAssistant: {
      name: 'Pomočnik agenta',
      description: 'Zgradite inteligentnega agenta, ki lahko samostojno izbere orodja za dokončanje nalog',
    },
    name: 'Vrsta pomočnika',
  },
  agent: {
    agentModeType: {
      functionCall: 'Klicanje funkcij',
      ReACT: 'Reagirajo',
    },
    setting: {
      maximumIterations: {
        description: 'Omejitev števila ponovitev, ki jih lahko izvede pomočnik agenta',
        name: 'Največje število ponovitev',
      },
      description: 'Nastavitve pomočnika za agente omogočajo nastavitev načina agenta in naprednih funkcij, kot so vgrajeni pozivi, ki so na voljo samo v vrsti agenta.',
      name: 'Nastavitve agenta',
    },
    tools: {
      enabled: 'Omogočeno',
      name: 'Orodja',
      description: 'Uporaba orodij lahko razširi zmogljivosti LLM, kot je iskanje po internetu ali izvajanje znanstvenih izračunov',
    },
    agentMode: 'Način agenta',
    promptPlaceholder: 'Tukaj napišite svoj poziv',
    agentModeDes: 'Nastavitev vrste načina sklepanja za agenta',
    firstPrompt: 'Prvi poziv',
    nextIteration: 'Naslednja ponovitev',
    buildInPrompt: 'Poziv za vgradnjo',
  },
  chatSubTitle: 'Navodila',
  variableTitle: 'Spremenljivke',
  completionSubTitle: 'Poziv za predpono',
  debugAsSingleModel: 'Odpravljanje napak kot en model',
  noResult: 'Tukaj bo prikazan izhod.',
  debugAsMultipleModel: 'Odpravljanje napak kot več modelov',
  formattingChangedText: 'Spreminjanje oblikovanja bo ponastavilo območje za odpravljanje napak, ste prepričani?',
  autoAddVar: 'Nedoločene spremenljivke, na katere se sklicuje vnaprejšnji poziv, ali jih želite dodati v obrazec za vnos uporabnika?',
  formattingChangedTitle: 'Spremenjeno oblikovanje',
  duplicateModel: 'Dvojnik',
  publishAs: 'Objavi kot',
  result: 'Izhodno besedilo',
  variableTip: 'Uporabniki izpolnijo spremenljivke v obrazcu in samodejno zamenjajo spremenljivke v pozivu.',
}

export default translation
