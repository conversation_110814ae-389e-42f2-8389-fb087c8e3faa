const translation = {
  feature: {
    fileUpload: {
      title: 'การอัปโหลดไฟล์',
      description: 'กล่องข้อความแชทช่วยให้สามารถอัปโหลดรูปภาพ เอกสาร และไฟล์อื่นๆ ได้',
      supportedTypes: 'ประเภทไฟล์ที่รองรับ',
      numberLimit: 'จำนวนสูงสุดที่อัปโหลดได้',
      modalTitle: 'การตั้งค่าการอัปโหลดไฟล์',
    },
    imageUpload: {
      title: 'การอัปโหลดรูปภาพ',
      description: 'อนุญาตให้อัปโหลดรูปภาพได้',
      supportedTypes: 'ประเภทไฟล์ที่รองรับ',
      numberLimit: 'จำนวนสูงสุดที่อัปโหลดได้',
      modalTitle: 'การตั้งค่าการอัปโหลดรูปภาพ',
    },
    bar: {
      empty: 'เปิดใช้งานคุณสมบัติเพื่อเพิ่มประสบการณ์ผู้ใช้ของเว็บแอป',
      enableText: 'เปิดใช้งานคุณสมบัติแล้ว',
      manage: 'จัดการ',
    },
    documentUpload: {
      title: 'เอกสาร',
      description: 'การเปิดใช้งานเอกสารจะทำให้โมเดลสามารถรับเอกสารและตอบคำถามเกี่ยวกับเอกสารเหล่านั้นได้',
    },
    audioUpload: {
      title: 'เสียง',
      description: 'การเปิดใช้งานเสียงจะทำให้โมเดลสามารถประมวลผลไฟล์เสียงเพื่อการถอดข้อความและการวิเคราะห์ได้',
    },
    groupChat: {
      title: 'ปรับปรุงแชท',
      description: 'เพิ่มการตั้งค่าก่อนการสนทนาสําหรับแอปสามารถปรับปรุงประสบการณ์ของผู้ใช้ได้',
    },
    groupExperience: {
      title: 'ปรับปรุงประสบการณ์',
    },
    conversationOpener: {
      description: 'ในแอปแชท ประโยคแรกที่ AI พูดกับผู้ใช้อย่างแข็งขันมักจะใช้เป็นการต้อนรับ',
      title: 'ที่เปิดการสนทนา',
    },
    suggestedQuestionsAfterAnswer: {
      title: 'ติดตาม',
      resDes: '3 ข้อเสนอแนะสําหรับผู้ใช้คําถามถัดไป',
      tryToAsk: 'ลองถาม',
      description: 'การตั้งค่าคําแนะนําคําถามถัดไปจะช่วยให้ผู้ใช้แชทได้ดีขึ้น',
    },
    moreLikeThis: {
      title: 'เพิ่มเติมเช่นนี้',
      tip: 'การใช้คุณสมบัตินี้จะมีค่าใช้จ่ายโทเค็นเพิ่มเติม',
      generateNumTip: 'จํานวนครั้งที่สร้างขึ้นแต่ละครั้ง',
      description: 'สร้างข้อความหลายข้อความพร้อมกัน จากนั้นแก้ไขและสร้างต่อไป',
    },
    speechToText: {
      description: 'สามารถใช้การป้อนข้อมูลด้วยเสียงในการแชทได้',
      title: 'คําพูดเป็นข้อความ',
      resDes: 'เปิดใช้งานการป้อนข้อมูลด้วยเสียง',
    },
    textToSpeech: {
      title: 'ข้อความเป็นคําพูด',
      resDes: 'เปิดใช้งานข้อความเป็นเสียง',
      description: 'ข้อความการสนทนาสามารถแปลงเป็นคําพูดได้',
    },
    citation: {
      title: 'การอ้างอิงและการระบุแหล่งที่มา',
      resDes: 'เปิดใช้งานการอ้างอิงและการระบุแหล่งที่มา',
      description: 'แสดงเอกสารต้นฉบับและส่วนที่มาของเนื้อหาที่สร้างขึ้น',
    },
    annotation: {
      scoreThreshold: {
        accurateMatch: 'การจับคู่ที่แม่นยํา',
        description: 'ใช้เพื่อกําหนดเกณฑ์ความคล้ายคลึงกันสําหรับการตอบกลับคําอธิบายประกอบ',
        easyMatch: 'จับคู่ง่าย',
        title: 'เกณฑ์คะแนน',
      },
      matchVariable: {
        title: 'ตัวแปรการจับคู่',
        choosePlaceholder: 'เลือกตัวแปรการจับคู่',
      },
      removeConfirm: 'ลบคําอธิบายประกอบนี้ ?',
      cacheManagement: 'คำ อธิบาย',
      title: 'คําอธิบายประกอบ ตอบกลับ',
      remove: 'ถอด',
      resDes: 'เปิดใช้งานการตอบสนองคําอธิบายประกอบ',
      add: 'เพิ่มคําอธิบายประกอบ',
      edit: 'แก้ไขคําอธิบายประกอบ',
      cached: 'มีคําอธิบายประกอบ',
      description: 'คุณสามารถเพิ่มการตอบกลับคุณภาพสูงลงในแคชด้วยตนเองเพื่อจับคู่ลําดับความสําคัญกับคําถามของผู้ใช้ที่คล้ายกัน',
    },
    dataSet: {
      queryVariable: {
        ok: 'ตกลง, ได้',
        noVar: 'ไม่ใช่ตัวแปร',
        choosePlaceholder: 'เลือกตัวแปรแบบสอบถาม',
        tip: 'ตัวแปรนี้จะถูกใช้เป็นอินพุตแบบสอบถามสําหรับการดึงบริบท โดยรับข้อมูลบริบทที่เกี่ยวข้องกับอินพุตของตัวแปรนี้',
        unableToQueryDataSetTip: 'ไม่สามารถสืบค้นความรู้ได้สําเร็จ โปรดเลือกตัวแปรการสืบค้นบริบทในส่วนบริบท',
        noVarTip: 'โปรดสร้างตัวแปรภายใต้ส่วนตัวแปร',
        title: 'ตัวแปรคิวรี',
        contextVarNotEmpty: 'ตัวแปรการสืบค้นบริบทต้องไม่ว่างเปล่า',
        deleteContextVarTip: 'ตัวแปรนี้ถูกตั้งค่าเป็นตัวแปรแบบสอบถามบริบท และการลบตัวแปรนี้จะส่งผลต่อการใช้ความรู้ตามปกติ หากคุณยังต้องการลบ โปรดเลือกใหม่ในส่วนบริบท',
        unableToQueryDataSet: 'ไม่สามารถสืบค้นความรู้ได้',
      },
      noDataSet: 'ไม่พบความรู้',
      notSupportSelectMulti: 'ปัจจุบันรองรับความรู้เพียงหนึ่งความรู้',
      selected: 'เลือกความรู้',
      title: 'ความรู้',
      toCreate: 'ไปที่สร้าง',
      words: 'นิรุกติ',
      textBlocks: 'บล็อกข้อความ',
      noData: 'คุณสามารถนําเข้าความรู้เป็นบริบทได้',
      selectTitle: 'เลือกข้อมูลอ้างอิง ความรู้',
    },
    tools: {
      modal: {
        toolType: {
          title: 'ประเภทเครื่องมือ',
          placeholder: 'โปรดเลือกประเภทเครื่องมือ',
        },
        name: {
          title: 'ชื่อ',
          placeholder: 'กรุณากรอกชื่อ',
        },
        variableName: {
          title: 'ชื่อตัวแปร',
          placeholder: 'กรุณากรอกชื่อตัวแปร',
        },
        title: 'เครื่องมือ',
      },
      title: 'เครื่อง มือ',
      tips: 'เครื่องมือมีวิธีการเรียก API มาตรฐาน โดยใช้อินพุตหรือตัวแปรของผู้ใช้เป็นพารามิเตอร์คําขอสําหรับการสืบค้นข้อมูลภายนอกตามบริบท',
    },
    conversationHistory: {
      editModal: {
        userPrefix: 'คํานําหน้าผู้ใช้',
        title: 'แก้ไขชื่อบทบาทการสนทนา',
        assistantPrefix: 'คํานําหน้าผู้ช่วย',
      },
      description: 'ตั้งชื่อคํานําหน้าสําหรับบทบาทการสนทนา',
      learnMore: 'ศึกษาเพิ่มเติม',
      title: 'ประวัติการสนทนา',
    },
    toolbox: {
      title: 'เครื่อง มือ',
    },
    moderation: {
      modal: {
        provider: {
          openaiTip: {
            suffix: '.',
            prefix: 'การกลั่นกรอง OpenAI ต้องใช้คีย์ OpenAI API ที่กําหนดค่าไว้ใน',
          },
          keywords: 'คำ',
          openai: 'การกลั่นกรอง OpenAI',
          title: 'ผู้จัดหา',
        },
        keywords: {
          placeholder: 'หนึ่งบรรทัดต่อบรรทัดคั่นด้วยตัวแบ่งบรรทัด',
          tip: 'หนึ่งบรรทัด คั่นด้วยตัวแบ่งบรรทัด สูงสุด 100 อักขระต่อบรรทัด',
          line: 'สาย',
        },
        content: {
          output: 'เนื้อหา OUTPUT ปานกลาง',
          errorMessage: 'การตอบกลับที่ตั้งไว้ล่วงหน้าต้องไม่ว่างเปล่า',
          fromApi: 'การตอบกลับที่ตั้งไว้ล่วงหน้าจะถูกส่งคืนโดย API',
          supportMarkdown: 'รองรับ Markdown',
          placeholder: 'เนื้อหาตอบกลับที่ตั้งไว้ล่วงหน้าที่นี่',
          condition: 'เปิดใช้งานเนื้อหา INPUT และ OUTPUT กลั่นกรองอย่างน้อยหนึ่งรายการ',
          input: 'กลั่นกรองเนื้อหา INPUT',
          preset: 'การตอบกลับที่ตั้งไว้ล่วงหน้า',
        },
        openaiNotConfig: {
          after: '',
          before: 'การกลั่นกรอง OpenAI ต้องใช้คีย์ OpenAI API ที่กําหนดค่าไว้ใน',
        },
        title: 'การตั้งค่าการกลั่นกรองเนื้อหา',
      },
      contentEnableLabel: 'การกลั่นกรองเนื้อหาเปิดใช้งานแล้ว',
      outputEnabled: 'ผลิตภัณฑ์',
      title: 'การกลั่นกรองเนื้อหา',
      allEnabled: 'อินพุต & เอาต์พุต',
      inputEnabled: 'อินพุต',
      description: 'รักษาความปลอดภัยเอาต์พุตโมเดลโดยใช้ API การกลั่นกรองหรือรักษารายการคําที่ละเอียดอ่อน',
    },
  },
  pageTitle: {
    line1: 'พร้อมท์',
    line2: 'วิศวกรรม',
  },
  promptMode: {
    advancedWarning: {
      ok: 'ตกลง, ได้',
      description: 'ในโหมดผู้เชี่ยวชาญ คุณสามารถแก้ไข PROMPT ทั้งหมดได้',
      title: 'คุณได้เปลี่ยนเป็นโหมดผู้เชี่ยวชาญแล้ว และเมื่อคุณแก้ไข PROMPT แล้ว คุณจะไม่สามารถกลับสู่โหมดพื้นฐานได้',
      learnMore: 'ศึกษาเพิ่มเติม',
    },
    operation: {
      addMessage: 'เพิ่มข้อความ',
    },
    switchBack: 'สลับกลับ',
    contextMissing: 'องค์ประกอบบริบทที่พลาดไปประสิทธิภาพของพรอมต์อาจไม่ดี',
    simple: 'เปลี่ยนเป็นโหมดผู้เชี่ยวชาญเพื่อแก้ไข PROMPT ทั้งหมด',
    advanced: 'แฟชั่นผู้เชี่ยวชาญ',
  },
  operation: {
    automatic: 'ผลิต',
    applyConfig: 'ตีพิมพ์',
    disagree: 'ไม่ชอบ',
    userAction: 'ผู้ใช้',
    stopResponding: 'หยุดการตอบสนอง',
    cancelAgree: 'ยกเลิกถูกใจ',
    addFeature: 'เพิ่มคุณสมบัติ',
    cancelDisagree: 'ยกเลิกการไม่ชอบ',
    agree: 'ชอบ',
    resetConfig: 'รี เซ็ต',
    debugConfig: 'ดีบัก',
  },
  notSetAPIKey: {
    settingBtn: 'ไปที่การตั้งค่า',
    trailFinished: 'เส้นทางเสร็จสิ้น',
    description: 'ยังไม่ได้ตั้งค่าคีย์ผู้ให้บริการ LLM และจําเป็นต้องตั้งค่าก่อนการดีบัก',
    title: 'ไม่ได้ตั้งค่าคีย์ผู้ให้บริการ LLM',
  },
  trailUseGPT4Info: {
    description: 'ใช้ gpt-4 โปรดตั้งค่าคีย์ API',
    title: 'ไม่รองรับ gpt-4 ในขณะนี้',
  },
  codegen: {
    applyChanges: 'ใช้การเปลี่ยนแปลง',
    generate: 'ผลิต',
    instructionPlaceholder: 'ป้อนคําอธิบายโดยละเอียดของรหัสที่คุณต้องการสร้าง',
    noDataLine1: 'อธิบายกรณีการใช้งานของคุณทางด้านซ้าย',
    title: 'เครื่องสร้างรหัส',
    overwriteConfirmMessage: 'การดําเนินการนี้จะเขียนทับโค้ดที่มีอยู่ คุณต้องการดําเนินการต่อหรือไม่?',
    loading: 'กําลังสร้างโค้ด...',
    generatedCodeTitle: 'รหัสที่สร้างขึ้น',
    apply: 'ใช้',
    overwriteConfirmTitle: 'เขียนทับรหัสที่มีอยู่ใช่ไหม',
    instruction: 'คำ แนะ นำ',
    resTitle: 'รหัสที่สร้างขึ้น',
    noDataLine2: 'ตัวอย่างโค้ดจะแสดงที่นี่',
    description: 'ตัวสร้างโค้ดใช้โมเดลที่กําหนดค่าเพื่อสร้างโค้ดคุณภาพสูงตามคําแนะนําของคุณ โปรดให้คําแนะนําที่ชัดเจนและละเอียด',
  },
  generate: {
    template: {
      pythonDebugger: {
        name: 'ดีบักเกอร์ Python',
        instruction: 'บอทที่สามารถสร้างและแก้ไขข้อบกพร่องโค้ดของคุณตามคําสั่งของคุณ',
      },
      translation: {
        instruction: 'นักแปลที่สามารถแปลได้หลายภาษา',
        name: 'การแปล',
      },
      professionalAnalyst: {
        name: 'นักวิเคราะห์มืออาชีพ',
        instruction: 'ดึงข้อมูลเชิงลึก ระบุความเสี่ยง และกลั่นกรองข้อมูลสําคัญจากรายงานขนาดยาวลงในบันทึกเดียว',
      },
      excelFormulaExpert: {
        name: 'ผู้เชี่ยวชาญด้านสูตร Excel',
        instruction: 'แชทบอทที่สามารถช่วยให้ผู้ใช้มือใหม่เข้าใจ ใช้ และสร้างสูตร Excel ตามคําแนะนําของผู้ใช้',
      },
      travelPlanning: {
        name: 'การวางแผนการเดินทาง',
        instruction: 'ผู้ช่วยวางแผนการเดินทางเป็นเครื่องมืออัจฉริยะที่ออกแบบมาเพื่อช่วยให้ผู้ใช้วางแผนการเดินทางได้อย่างง่ายดาย',
      },
      SQLSorcerer: {
        name: 'พ่อมด SQL',
        instruction: 'แปลงภาษาในชีวิตประจําวันให้เป็นแบบสอบถาม SQL',
      },
      GitGud: {
        name: 'กิต gud',
        instruction: 'สร้างคําสั่ง Git ที่เหมาะสมตามการดําเนินการควบคุมเวอร์ชันที่ผู้ใช้อธิบาย',
      },
      meetingTakeaways: {
        name: 'ประเด็นการประชุม',
        instruction: 'กลั่นกรองการประชุมเป็นบทสรุปที่กระชับ รวมถึงหัวข้อการสนทนา ประเด็นสําคัญ และรายการปฏิบัติ',
      },
      writingsPolisher: {
        name: 'เครื่องขัดเขียน',
        instruction: 'ใช้เทคนิคการแก้ไขคําโฆษณาขั้นสูงเพื่อปรับปรุงงานเขียนของคุณ',
      },
    },
    generate: 'ผลิต',
    instruction: 'คำ แนะ นำ',
    apply: 'ใช้',
    resTitle: 'พรอมต์ที่สร้างขึ้น',
    title: 'เครื่องกําเนิดพร้อมท์',
    noDataLine2: 'ตัวอย่างการประสานเสียงจะแสดงที่นี่',
    tryIt: 'ลองดู',
    overwriteTitle: 'แทนที่การกําหนดค่าที่มีอยู่ใช่ไหม',
    noDataLine1: 'อธิบายกรณีการใช้งานของคุณทางด้านซ้าย',
    instructionPlaceHolder: 'เขียนคําแนะนําที่ชัดเจนและเฉพาะเจาะจง',
    overwriteMessage: 'การใช้พรอมต์นี้จะแทนที่การกําหนดค่าที่มีอยู่',
    description: 'ตัวสร้างพรอมต์ใช้โมเดลที่กําหนดค่าเพื่อปรับพรอมต์ให้เหมาะสมเพื่อคุณภาพที่สูงขึ้นและโครงสร้างที่ดีขึ้น โปรดเขียนคําแนะนําที่ชัดเจนและละเอียด',
    loading: 'กําลังประสานงานแอปพลิเคชันสําหรับคุณ...',
    latest: 'ล่าสุด',
    dismiss: 'ปฏิเสธ',
    optional: 'ตัวเลือก',
    optimizationNote: 'หมายเหตุการปรับแต่ง',
    press: 'กด',
    idealOutput: 'ผลลัพธ์ที่เหมาะสม',
    to: 'ไป',
    instructionPlaceHolderLine2: 'รูปแบบการส่งออกไม่ถูกต้อง กรุณาปฏิบัติตามรูปแบบ JSON อย่างเคร่งครัด.',
    versions: 'เวอร์ชัน',
    newNoDataLine1: 'เขียนคำแนะนำในคอลัมน์ซ้าย และคลิกที่สร้างเพื่อดูผลลัพธ์.',
    instructionPlaceHolderLine3: 'โทนเสียงดูเข้มเกินไป กรุณาทำให้มันเป็นกันเองมากขึ้น',
    instructionPlaceHolderLine1: 'ทำให้ผลลัพธ์กระชับขึ้น โดยคงสาระสำคัญไว้',
    instructionPlaceHolderTitle: 'อธิบายว่าคุณต้องการปรับปรุง Prompt นี้อย่างไร ตัวอย่างเช่น:',
    version: 'เวอร์ชัน',
    insertContext: 'แทรกบริบท',
    idealOutputPlaceholder: 'โปรดอธิบายรูปแบบการตอบสนองที่คุณต้องการ ความยาว โทนเสียง และความต้องการเนื้อหา...',
    optimizePromptTooltip: 'ปรับปรุงในเครื่องกำเนิดคำแนะนำ',
    codeGenInstructionPlaceHolderLine: 'ยิ่งข้อเสนอแนะแน่นอนมากขึ้น เช่น ประเภทของข้อมูลที่เป็นอินพุตและเอาต์พุต รวมถึงวิธีการที่ตัวแปรถูกประมวลผล การสร้างโค้ดจะยิ่งแม่นยำมากขึ้น',
  },
  resetConfig: {
    title: 'ยืนยันการรีเซ็ต?',
    message: 'รีเซ็ตจะละทิ้งการเปลี่ยนแปลง โดยคืนค่าการกําหนดค่าที่เผยแพร่ล่าสุด',
  },
  errorMessage: {
    waitForFileUpload: 'โปรดรอให้ไฟล์/ไฟล์อัปโหลด',
    notSelectModel: 'โปรดเลือกรุ่น',
    waitForBatchResponse: 'โปรดรอให้การตอบกลับงานแบทช์เสร็จสมบูรณ์',
    waitForResponse: 'โปรดรอให้การตอบกลับข้อความก่อนหน้าเสร็จสมบูรณ์',
    waitForImgUpload: 'โปรดรอให้ภาพอัปโหลด',
    queryRequired: 'ต้องส่งข้อความคําขอ',
  },
  warningMessage: {
    timeoutExceeded: 'ผลลัพธ์จะไม่แสดงเนื่องจากหมดเวลา โปรดดูบันทึกเพื่อรวบรวมผลลัพธ์ที่สมบูรณ์',
  },
  variableTable: {
    optional: 'เสริม',
    key: 'ปุ่มตัวแปร',
    typeString: 'เชือก',
    typeSelect: 'เลือก',
    type: 'ประเภทอินพุต',
    name: 'ชื่อฟิลด์ป้อนข้อมูลของผู้ใช้',
    action: 'การดําเนินการ',
  },
  varKeyError: {},
  otherError: {
    queryNoBeEmpty: 'ต้องตั้งค่าคิวรีในพร้อมท์',
    promptNoBeEmpty: 'พรอมต์ไม่สามารถว่างเปล่าได้',
    historyNoBeEmpty: 'ต้องตั้งค่าประวัติการสนทนาในข้อความแจ้ง',
  },
  variableConfig: {
    'file': {
      image: {
        name: 'ภาพ',
      },
      audio: {
        name: 'เสียง',
      },
      document: {
        name: 'เอกสาร',
      },
      video: {
        name: 'วีดิทัศน์',
      },
      custom: {
        description: 'ระบุประเภทไฟล์อื่นๆ',
        name: 'ไฟล์ประเภทอื่น ๆ',
        createPlaceholder: '  นามสกุลไฟล์ เช่น .doc',
      },
      supportFileTypes: 'ประเภทไฟล์ที่รองรับ',
    },
    'errorMsg': {
      atLeastOneOption: 'จําเป็นต้องมีอย่างน้อยหนึ่งตัวเลือก',
      labelNameRequired: 'ต้องมีชื่อฉลาก',
      optionRepeat: 'มีตัวเลือกการทําซ้ํา',
      varNameCanBeRepeat: 'ไม่สามารถทําซ้ําชื่อตัวแปรได้',
    },
    'hide': 'ซ่อน',
    'required': 'ต้องระบุ',
    'number': 'เลข',
    'inputPlaceholder': 'กรุณาป้อน',
    'uploadFileTypes': 'อัปโหลดประเภทไฟล์',
    'content': 'เนื้อหา',
    'addOption': 'เพิ่มตัวเลือก',
    'labelName': 'ชื่อฉลาก',
    'options': 'ตัวเลือก',
    'stringTitle': 'ตัวเลือกกล่องข้อความฟอร์ม',
    'noDefaultValue': 'ไม่มีค่าเริ่มต้น',
    'varName': 'ชื่อตัวแปร',
    'defaultValue': 'ค่าเริ่มต้น',
    'fieldType': 'ชนิดฟิลด์',
    'selectDefaultValue': 'เลือกค่าเริ่มต้น',
    'string': 'ข้อความสั้น',
    'text-input': 'ข้อความสั้น',
    'multi-files': 'รายการไฟล์',
    'maxLength': 'ความยาวสูงสุด',
    'addModalTitle': 'เพิ่มฟิลด์อินพุต',
    'localUpload': 'อัปโหลดในเครื่อง',
    'single-file': 'ไฟล์เดียว',
    'select': 'เลือก',
    'maxNumberOfUploads': 'จํานวนการอัปโหลดสูงสุด',
    'editModalTitle': 'แก้ไขฟิลด์อินพุต',
    'apiBasedVar': 'ตัวแปรที่ใช้ API',
    'paragraph': 'วรรค',
    'both': 'ทั้งสอง',
  },
  vision: {
    visionSettings: {
      resolution: 'มติ',
      uploadMethod: 'วิธีการอัปโหลด',
      localUpload: 'อัปโหลดในเครื่อง',
      low: 'ต่ํา',
      high: 'สูง',
      title: 'การตั้งค่าวิสัยทัศน์',
      uploadLimit: 'ขีดจํากัดการอัปโหลด',
      both: 'ทั้งสอง',
      url: 'URL',
    },
    onlySupportVisionModelTip: 'รองรับเฉพาะโมเดลการมองเห็น',
    name: 'การมองเห็น',
    description: 'เปิดใช้งานวิสัยทัศน์จะช่วยให้โมเดลสามารถถ่ายภาพและตอบคําถามเกี่ยวกับภาพเหล่านั้นได้',
    settings: 'การตั้งค่า',
  },
  voice: {
    voiceSettings: {
      autoPlayEnabled: 'บน',
      autoPlay: 'เล่นอัตโนมัติ',
      voice: 'เสียง',
      resolutionTooltip: 'ภาษาสนับสนุนเสียงแปลงข้อความเป็นคําพูด。',
      autoPlayDisabled: 'ไป',
      title: 'การตั้งค่าเสียง',
      language: 'ภาษา',
    },
    name: 'เสียง',
    settings: 'การตั้งค่า',
    description: 'การตั้งค่าเสียงข้อความเป็นคําพูด',
    defaultDisplay: 'เสียงเริ่มต้น',
  },
  openingStatement: {
    tooShort: 'ต้องใช้ข้อความแจ้งเริ่มต้นอย่างน้อย 20 คําเพื่อสร้างคําพูดเปิดการสนทนา',
    openingQuestion: 'คําถามเปิด',
    writeOpener: 'ตัวเปิดแก้ไข',
    add: 'เพิ่ม',
    title: 'ที่เปิดการสนทนา',
    noDataPlaceHolder: 'การเริ่มการสนทนากับผู้ใช้สามารถช่วยให้ AI สร้างความสัมพันธ์ที่ใกล้ชิดกับพวกเขาในแอปพลิเคชันการสนทนา',
  },
  modelConfig: {
    modeType: {
      completion: 'สมบูรณ์',
      chat: 'สนทนา',
    },
    model: 'แบบ',
    title: 'รุ่นและพารามิเตอร์',
    setTone: 'กําหนดน้ําเสียงของการตอบกลับ',
  },
  inputs: {
    run: 'วิ่ง',
    userInputField: 'ฟิลด์ป้อนข้อมูลของผู้ใช้',
    queryPlaceholder: 'กรุณากรอกข้อความคําขอ',
    queryTitle: 'เนื้อหาแบบสอบถาม',
    title: 'ดีบัก & ดูตัวอย่าง',
    noVar: 'กรอกค่าของตัวแปร ซึ่งจะถูกแทนที่โดยอัตโนมัติในคําพร้อมท์ทุกครั้งที่เริ่มเซสชันใหม่',
    previewTitle: 'พร้อมท์ดูตัวอย่าง',
    chatVarTip: 'กรอกค่าของตัวแปร ซึ่งจะถูกแทนที่โดยอัตโนมัติในคําพร้อมท์ทุกครั้งที่เริ่มเซสชันใหม่',
    noPrompt: 'ลองเขียนข้อความแจ้งในการป้อนข้อมูลล่วงหน้า',
    completionVarTip: 'กรอกค่าของตัวแปร ซึ่งจะถูกแทนที่โดยอัตโนมัติในคําพร้อมท์ทุกครั้งที่มีการส่งคําถาม',
  },
  datasetConfig: {
    retrieveOneWay: {
      title: 'การดึงข้อมูล N-to-1',
      description: 'เอเจนต์จะเลือกความรู้ที่ดีที่สุดสําหรับการสืบค้นด้วยตนเอง ดีที่สุดสําหรับการใช้งานที่มีความรู้ที่แตกต่างและจํากัด',
    },
    retrieveMultiWay: {
      title: 'การดึงข้อมูลหลายเส้นทาง',
      description: 'ตามความตั้งใจของผู้ใช้ การสืบค้นในความรู้ทั้งหมด ดึงข้อความที่เกี่ยวข้องจากหลายแหล่ง และเลือกผลลัพธ์ที่ดีที่สุดที่ตรงกับการสืบค้นของผู้ใช้หลังจากจัดอันดับใหม่',
    },
    score_thresholdTip: 'ใช้เพื่อกําหนดเกณฑ์ความคล้ายคลึงกันสําหรับการกรองกลุ่ม',
    settingTitle: 'การตั้งค่าการดึงข้อมูล',
    rerankModelRequired: 'จําเป็นต้องมีโมเดลจัดอันดับใหม่ที่กําหนดค่าไว้',
    knowledgeTip: 'คลิกปุ่ม " " เพื่อเพิ่มความรู้',
    embeddingModelRequired: 'จําเป็นต้องมีโมเดลการฝังที่กําหนดค่าไว้',
    score_threshold: 'เกณฑ์คะแนน',
    retrieveChangeTip: 'การปรับเปลี่ยนโหมดดัชนีและโหมดการดึงข้อมูลอาจส่งผลต่อแอปพลิเคชันที่เกี่ยวข้องกับความรู้นี้',
    top_k: 'ท็อป K',
    params: 'พารามิเตอร์',
    top_kTip: 'ใช้เพื่อกรองกลุ่มที่คล้ายกับคําถามของผู้ใช้มากที่สุด ระบบจะปรับค่าของ Top K แบบไดนามิกตาม max_tokens ของรุ่นที่เลือก',
  },
  assistantType: {
    chatAssistant: {
      name: 'ผู้ช่วยพื้นฐาน',
      description: 'สร้างผู้ช่วยตามแชทโดยใช้โมเดลภาษาขนาดใหญ่',
    },
    agentAssistant: {
      name: 'ผู้ช่วยตัวแทน',
      description: 'สร้างตัวแทนอัจฉริยะที่สามารถเลือกเครื่องมือเพื่อทํางานให้เสร็จได้โดยอัตโนมัติ',
    },
    name: 'ประเภทผู้ช่วย',
  },
  agent: {
    agentModeType: {
      functionCall: 'การเรียกฟังก์ชัน',
      ReACT: 'ตอบสนอง',
    },
    setting: {
      maximumIterations: {
        description: 'จํากัดจํานวนการทําซ้ําที่ผู้ช่วยตัวแทนสามารถดําเนินการได้',
        name: 'การทําซ้ําสูงสุด',
      },
      name: 'การตั้งค่าตัวแทน',
      description: 'การตั้งค่าผู้ช่วยตัวแทนอนุญาตให้ตั้งค่าโหมดตัวแทนและคุณสมบัติขั้นสูง เช่น ข้อความแจ้งในตัว ซึ่งใช้ได้เฉพาะในประเภทตัวแทนเท่านั้น',
    },
    tools: {
      enabled: 'เปิด',
      name: 'เครื่อง มือ',
      description: 'การใช้เครื่องมือสามารถขยายขีดความสามารถของ LLM ได้ เช่น การค้นหาทางอินเทอร์เน็ตหรือการคํานวณทางวิทยาศาสตร์',
    },
    agentMode: 'โหมดตัวแทน',
    firstPrompt: 'พรอมต์แรก',
    buildInPrompt: 'พรอมต์ในตัว',
    promptPlaceholder: 'เขียนข้อความแจ้งของคุณที่นี่',
    nextIteration: 'การทําซ้ําครั้งต่อไป',
    agentModeDes: 'ตั้งค่าประเภทของโหมดการอนุมานสําหรับตัวแทน',
  },
  orchestrate: 'ออเคสตร้า',
  variableTitle: 'ตัว แปร',
  noResult: 'ผลลัพธ์จะแสดงที่นี่',
  formattingChangedText: 'การแก้ไขการจัดรูปแบบจะรีเซ็ตพื้นที่ดีบัก คุณแน่ใจหรือไม่?',
  publishAs: 'เผยแพร่เป็น',
  result: 'ข้อความที่ส่งออก',
  formattingChangedTitle: 'การจัดรูปแบบเปลี่ยนไป',
  completionSubTitle: 'พรอมต์คํานําหน้า',
  chatSubTitle: 'คำ แนะ นำ',
  debugAsMultipleModel: 'ดีบักเป็นหลายรุ่น',
  variableTip: 'ผู้ใช้กรอกตัวแปรในแบบฟอร์ม แทนที่ตัวแปรในพรอมต์โดยอัตโนมัติ',
  debugAsSingleModel: 'ดีบักเป็นโมเดลเดียว',
  duplicateModel: 'สำเนา',
  autoAddVar: 'ตัวแปรที่ไม่ได้กําหนดอ้างอิงในพรอมต์ล่วงหน้าคุณต้องการเพิ่มในแบบฟอร์มการป้อนข้อมูลของผู้ใช้หรือไม่?',
}

export default translation
