const translation = {
  createApp: 'สร้างโปรเจกต์ใหม่',
  types: {
    all: 'ทั้งหมด',
    chatbot: 'แชทบอท',
    agent: 'ตัวแทน',
    workflow: 'กระบวนการทำงาน',
    completion: 'เสร็จสมบูรณ์',
    basic: 'พื้นฐาน',
    advanced: 'แชทโฟลว์',
  },
  duplicate: 'สำเนา',
  duplicateTitle: 'ชื่อซ้ำ',
  export: 'ส่งออก DSL',
  exportFailed: 'ส่งออก DSL ล้มเหลว',
  importDSL: 'นําเข้าไฟล์ DSL',
  createFromConfigFile: 'สร้างจากไฟล์ DSL',
  importFromDSL: 'นําเข้าจาก DSL',
  importFromDSLFile: 'จากไฟล์ DSL',
  importFromDSLUrl: 'จาก URL',
  importFromDSLUrlPlaceholder: 'วางลิงค์ DSL ที่นี่',
  dslUploader: {
    button: 'ลากและวางไฟล์ หรือ',
    browse: 'เรียกดู',
  },
  deleteAppConfirmTitle: 'ลบโปรเจกต์นี้?',
  deleteAppConfirmContent: 'การลบโปรเจกนั้นไม่สามารถย้อนกลับได้ ผู้ใช้จะไม่สามารถเข้าถึงโปรเจกต์ของคุณอีกต่อไป และการกําหนดค่าต่างๆและบันทึกทั้งหมดจะถูกลบอย่างถาวร',
  appDeleted: 'โปรเจกต์ถูกลบ',
  appDeleteFailed: 'ลบโปรเจกต์ไม่สําเร็จ',
  join: 'เข้าร่วมชุมชนนักพัฒนา',
  communityIntro: 'พูดคุยกับสมาชิกในทีม ผู้ร่วมให้ข้อมูล และนักพัฒนาในช่องทางต่างๆ',
  roadmap: 'ดูแผนงานของเรา',
  newApp: {
    startFromBlank: 'สร้างโปรเจกต์ปล่าว',
    startFromTemplate: 'สร้างจากเทมเพลต',
    workflowWarning: 'ขณะนี้อยู่ในช่วงเบต้า',
    captionName: 'ไอคอนและชื่อโปรเจกต์',
    appNamePlaceholder: 'ตั้งชื่อโปรเจกต์ของคุณ',
    captionDescription: 'คำอธิบาย',
    appDescriptionPlaceholder: 'ป้อนคำอธิบายของโปรเจกต์',
    useTemplate: 'ใช้เทมเพลตนี้',
    previewDemo: 'ตัวอย่างการใช้งาน',
    chatApp: 'ผู้ช่วย',
    chatAppIntro: 'ฉันต้องการสร้างโปรเจกต์ ที่เป็นแอปพลิเคชันที่ใช้การแชท โปรเจกต์นี้ใช้รูปแบบคำถามและคำตอบ ทําให้สามารถสนทนาต่อเนื่องได้หลายรอบ(Multi-turn)',
    agentAssistant: 'ผู้ช่วยใหม่',
    completeApp: 'เครื่องมือสร้างข้อความ',
    completeAppIntro: 'ฉันต้องการสร้างโปรเจกต์ที่ ที่สามารถสร้างข้อความคุณภาพสูงตามข้อความแจ้ง เช่น การสร้างบทความ สรุป การแปล และอื่นๆ',
    showTemplates: 'ฉันต้องการเลือกจากเทมเพลต',
    hideTemplates: 'กลับไปที่การเลือกโหมด',
    Create: 'สร้าง',
    Cancel: 'ยกเลิก',
    Confirm: 'ยืนยัน',
    nameNotEmpty: 'ชื่อต้องไม่ว่างเปล่า',
    appTemplateNotSelected: 'โปรดเลือกเทมเพลต',
    appTypeRequired: 'โปรดเลือกประเภทโปรเจกต์',
    appCreated: 'สร้างโปรเจกต์',
    caution: 'ข้อควรระวัง',
    appCreateDSLWarning: 'ข้อควรระวัง: ความแตกต่างของเวอร์ชัน DSL อาจส่งผลต่อคุณสมบัติบางอย่าง',
    appCreateDSLErrorTitle: 'ความเข้ากันไม่ได้ของ DSL เวอร์ชัน',
    appCreateDSLErrorPart1: 'ตรวจพบความแตกต่างอย่างมีนัยสําคัญในเวอร์ชัน DSL การบังคับนําเข้าอาจทําให้โปรเจกต์ทํางานผิดปกติ',
    appCreateDSLErrorPart2: 'คุณต้องการดําเนินการต่อหรือไม่?',
    appCreateDSLErrorPart3: 'เวอร์ชัน DSL ของโปรเจกต์ปัจจุบัน:',
    appCreateDSLErrorPart4: 'เวอร์ชัน DSL ที่ระบบรองรับ:',
    appCreateFailed: 'สร้างโปรเจกต์ไม่สําเร็จ',
    learnMore: 'ศึกษาเพิ่มเติม',
    foundResults: '{{count}} ผลลัพธ์',
    noTemplateFoundTip: 'ลองค้นหาโดยใช้คีย์เวิร์ดอื่น',
    chatbotShortDescription: 'แชทบอทที่ใช้ LLM พร้อมการตั้งค่าที่ง่ายดาย',
    optional: 'เสริม',
    workflowUserDescription: 'สร้างโฟลว์ AI อัตโนมัติด้วยระบบลากและวางอย่างง่าย',
    agentShortDescription: 'ตัวแทนอัจฉริยะพร้อมการใช้เหตุผลและเครื่องมืออัตโนมัติ',
    forBeginners: 'ประเภทแอปพื้นฐาน',
    completionShortDescription: 'ผู้ช่วย AI สําหรับงานสร้างข้อความ',
    agentUserDescription: 'ตัวแทนอัจฉริยะที่สามารถให้เหตุผลซ้ําๆ และใช้เครื่องมืออัตโนมัติเพื่อให้บรรลุเป้าหมายของงาน',
    noIdeaTip: 'ไม่มีความคิด? ดูเทมเพลตของเรา',
    foundResult: '{{count}} ผล',
    noAppsFound: 'ไม่พบแอป',
    workflowShortDescription: 'โฟลว์อัตโนมัติสำหรับระบบอัจฉริยะ',
    forAdvanced: 'สําหรับผู้ใช้ขั้นสูง',
    chatbotUserDescription: 'สร้างแชทบอทที่ใช้ LLM ได้อย่างรวดเร็วด้วยการกําหนดค่าที่ง่าย คุณสามารถเปลี่ยนไปใช้ Chatflow ได้ในภายหลัง',
    noTemplateFound: 'ไม่พบเทมเพลต',
    completionUserDescription: 'สร้างผู้ช่วย AI สําหรับงานสร้างข้อความอย่างรวดเร็วด้วยการกําหนดค่าที่ง่าย',
    advancedUserDescription: 'โฟลว์พร้อมคุณสมบัติหน่วยความจำเพิ่มเติมและอินเตอร์เฟซแชทบอท',
    chooseAppType: 'เลือกประเภทแอป',
    advancedShortDescription: 'โฟลว์ที่เสริมประสิทธิภาพสำหรับการสนทนาหลายรอบ',
    dropDSLToCreateApp: 'ลากไฟล์ DSL มาที่นี่เพื่สร้างแอป',
  },
  editApp: 'แก้ไขข้อมูล',
  editAppTitle: 'แก้ไขข้อมูลโปรเจกต์',
  editDone: 'อัปเดตข้อมูลโปรเจกต์',
  editFailed: 'อัปเดตข้อมูลโปรเจกต์ไม่สําเร็จ',
  iconPicker: {
    ok: 'ตกลง, ได้',
    cancel: 'ยกเลิก',
    emoji: 'อิโมจิ',
    image: 'ภาพ',
  },
  answerIcon: {
    title: 'ใช้ไอคอน web app เพื่อแทนที่ 🤖',
    description: 'จะใช้ไอคอน web app เพื่อแทนที่🤖ในโปรเจกต์ที่ใช้ร่วมกันหรือไม่',
    descriptionInExplore: 'จะใช้ไอคอน web app เพื่อแทนที่🤖ใน Explore หรือไม่',
  },
  switch: 'เปลี่ยนไปใช้ Workflow Orchestrate',
  switchTipStart: 'สําเนาโปรเจกต์ใหม่จะถูกสร้างขึ้นสําหรับคุณ และสําเนาใหม่จะเปลี่ยนเป็น Workflow Orchestration',
  switchTip: 'ไม่อนุญาต',
  switchTipEnd: 'เปลี่ยนกลับเป็น Basic Orchestrate',
  switchLabel: 'สําเนาโปรเจกต์ที่จะสร้าง',
  removeOriginal: 'ลบโปรเจกต์เดิม',
  switchStart: 'สวิตช์สตาร์ท',
  typeSelector: {
    all: 'ทุกประเภท',
    chatbot: 'แชทบอท',
    agent: 'ตัวแทน',
    workflow: 'เวิร์กโฟลว์',
    completion: 'เสร็จ',
    advanced: 'แชทโฟลว์',
  },
  tracing: {
    title: 'การติดตามประสิทธิภาพของโปรเจกต์',
    description: 'การกําหนดค่าผู้ให้บริการ LLMOps บุคคลที่สามและประสิทธิภาพของโปรเจกต์ที่นำไปใช้',
    config: 'กําหนดค่า',
    view: 'มุมมอง',
    collapse: 'ยุบ',
    expand: 'ขยาย',
    tracing: 'ติดตาม',
    disabled: 'ปิดการใช้งาน',
    disabledTip: 'โปรดกําหนดค่าผู้ให้บริการก่อน',
    enabled: 'ให้บริการ',
    tracingDescription: 'บันทึกบริบททั้งหมดของการดําเนินการของโปรเจกต์ รวมถึงการเรียก LLM, Prompt คําขอ HTTP และอื่นๆไปยังแพลตฟอร์มของของบุคคลที่สาม',
    configProviderTitle: {
      configured: 'กําหนดค่าแล้ว',
      notConfigured: 'ผู้ให้บริการกําหนดค่าเพื่อเปิดใช้งานการติดตาม',
      moreProvider: 'ผู้ให้บริการเพิ่มเติม',
    },
    arize: {
      title: 'Arize',
      description: 'การสังเกตการณ์ LLM ระดับองค์กร การประเมินออนไลน์และออฟไลน์ การตรวจสอบ และการทดลอง—ขับเคลื่อนโดย OpenTelemetry ออกแบบมาโดยเฉพาะสำหรับแอปพลิเคชันที่ขับเคลื่อนด้วย LLM และตัวแทน',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'แพลตฟอร์มโอเพ่นซอร์สและ OpenTelemetry สำหรับการสังเกตการณ์ การประเมิน วิศวกรรมพรอมต์ และการทดลองสำหรับเวิร์กโฟลว์และตัวแทน LLM ของคุณ',
    },
    langsmith: {
      title: 'Langsmith',
      description: 'แพลตฟอร์มนักพัฒนาแบบครบวงจรสําหรับทุกขั้นตอนของ การพัฒนาโปรเจกต์ที่ขับเคลื่อนด้วย LLM',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'การติดตาม การประเมินการจัดการพร้อมท์ และเมตริกเพื่อแก้ไขข้อบกพร่องและปรับปรุงโปรเจกต์ LLM ของคุณ',
    },
    inUse: 'ใช้งาน',
    configProvider: {
      title: 'กําหนดค่า',
      placeholder: 'ป้อน {{key}} ของคุณ',
      project: 'โครงการ',
      publicKey: 'กุญแจสาธารณะ',
      secretKey: 'กุญแจลับ',
      viewDocsLink: 'ดูเอกสาร {{key}}',
      removeConfirmTitle: 'ลบการกําหนดค่า {{key}} หรือไม่?',
      removeConfirmContent: 'การกําหนดค่าปัจจุบันกําลังใช้งาน การลบออกจะเป็นการปิดคุณสมบัติการติดตาม',
    },
    opik: {
      title: 'โอปิก',
      description: 'Opik เป็นแพลตฟอร์มโอเพ่นซอร์สสําหรับการประเมิน ทดสอบ และตรวจสอบแอปพลิเคชัน LLM',
    },
    weave: {
      title: 'ทอ',
      description: 'Weave เป็นแพลตฟอร์มโอเพนซอร์สสำหรับการประเมินผล ทดสอบ และตรวจสอบแอปพลิเคชัน LLM',
    },
    aliyun: {
      title: 'การตรวจสอบคลาวด์',
      description: 'แพลตฟอร์มการสังเกตการณ์ที่จัดการโดย Alibaba Cloud ซึ่งไม่ต้องดูแลและบำรุงรักษา ช่วยให้สามารถติดตาม ตรวจสอบ และประเมินแอปพลิเคชัน Dify ได้ทันที',
    },
  },
  mermaid: {
    handDrawn: 'วาดด้วยมือ',
    classic: 'คลาสสิก',
  },
  openInExplore: 'เปิดใน Explore',
  newAppFromTemplate: {
    sidebar: {
      Assistant: 'ผู้ช่วย',
      Writing: 'การเขียน',
      Recommended: 'แนะ นำ',
      Workflow: 'เวิร์กโฟลว์',
      Programming: 'โปรแกรม',
      HR: 'ชั่วโมง',
      Agent: 'ตัวแทน',
    },
    searchAllTemplate: 'ค้นหาเทมเพลตทั้งหมด...',
    byCategories: 'ตามหมวดหมู่',
  },
  showMyCreatedAppsOnly: 'แสดงเฉพาะแอปที่ฉันสร้าง',
  appSelector: {
    placeholder: 'เลือกแอป...',
    params: 'พารามิเตอร์แอพ',
    noParams: 'ไม่จําเป็นต้องใช้พารามิเตอร์',
    label: 'แอพ',
  },
  structOutput: {
    notConfiguredTip: 'ยังไม่ได้กำหนดผลลัพธ์ที่มีโครงสร้าง',
    moreFillTip: 'แสดงระดับการซ้อนสูงสุด 10 ระดับ',
    structuredTip: 'Structured Outputs เป็นฟีเจอร์ที่ทำให้มั่นใจว่าโมเดลจะสร้างคำตอบที่สอดคล้องกับ JSON Schema ที่คุณกำหนดไว้เสมอ',
    configure: 'กำหนดค่า',
    required: 'ที่จำเป็น',
    LLMResponse: 'LLM ตอบสนอง',
    structured: 'มีระเบียบ',
    modelNotSupported: 'โมเดลไม่ได้รับการสนับสนุน',
    modelNotSupportedTip: 'โมเดลปัจจุบันไม่รองรับฟีเจอร์นี้และจะถูกลดระดับเป็นการฉีดคำสั่งโดยอัตโนมัติ.',
  },
  accessItemsDescription: {
    anyone: 'ใครก็สามารถเข้าถึงเว็บแอปได้',
    specific: 'สมาชิกหรือกลุ่มเฉพาะเท่านั้นที่สามารถเข้าถึงแอปเว็บได้',
    organization: 'ใครก็ได้ในองค์กรสามารถเข้าถึงแอปเว็บได้',
    external: 'ผู้ใช้งานภายนอกที่ได้รับการยืนยันตัวตนเท่านั้นที่สามารถเข้าถึงแอปพลิเคชันเว็บได้',
  },
  accessControlDialog: {
    accessItems: {
      specific: 'กลุ่มหรือสมาชิกเฉพาะ',
      organization: 'เฉพาะสมาชิกภายในองค์กร',
      anyone: 'ใครก็ตามที่มีลิงก์',
      external: 'ผู้ใช้ภายนอกที่ได้รับการตรวจสอบแล้ว',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'ค้นหากลุ่มและสมาชิก',
      allMembers: 'สมาชิกทั้งหมด',
      noResult: 'ไม่มีผลลัพธ์',
      expand: 'ขยาย',
    },
    title: 'การควบคุมการเข้าถึงเว็บแอปพลิเคชัน',
    description: 'ตั้งค่าสิทธิ์การเข้าถึงเว็บแอป',
    accessLabel: 'ใครมีสิทธิ์เข้าถึง',
    groups_one: '{{count}} กลุ่ม',
    groups_other: '{{count}} กลุ่ม',
    members_one: '{{count}} สมาชิก',
    noGroupsOrMembers: 'ไม่มีกลุ่มหรือสมาชิกที่เลือก',
    webAppSSONotEnabledTip: 'กรุณาติดต่อผู้ดูแลระบบองค์กรเพื่อกำหนดวิธีการตรวจสอบสิทธิ์แอปเว็บ.',
    updateSuccess: 'อัปเดตสำเร็จแล้ว',
    members_other: '{{count}} สมาชิก',
  },
  publishApp: {
    title: 'ใครสามารถเข้าถึงแอปเว็บได้',
    notSet: 'ยังไม่ได้ตั้งค่า',
    notSetDesc: 'ขณะนี้ไม่มีใครสามารถเข้าถึงแอปเว็บได้ กรุณาเพิ่มสิทธิ์การเข้าถึง.',
  },
  accessControl: 'การควบคุมการเข้าถึงเว็บแอปพลิเคชัน',
  noAccessPermission: 'ไม่มีสิทธิ์เข้าถึงเว็บแอป',
  maxActiveRequestsPlaceholder: 'ใส่ 0 สำหรับไม่จำกัด',
  maxActiveRequests: 'จำนวนคำขอพร้อมกันสูงสุด',
  maxActiveRequestsTip: 'จำนวนการร้องขอที่ใช้งานพร้อมกันสูงสุดต่อแอป (0 หมายถึงไม่จำกัด)',
  gotoAnything: {
    actions: {
      searchKnowledgeBases: 'ค้นหาฐานความรู้',
      searchPlugins: 'ค้นหาปลั๊กอิน',
      searchWorkflowNodes: 'ค้นหาโหนดเวิร์กโฟลว์',
      searchApplications: 'ค้นหาแอปพลิเคชัน',
      searchKnowledgeBasesDesc: 'ค้นหาและนําทางไปยังฐานความรู้ของคุณ',
      searchPluginsDesc: 'ค้นหาและนําทางไปยังปลั๊กอินของคุณ',
      searchApplicationsDesc: 'ค้นหาและนําทางไปยังแอปพลิเคชันของคุณ',
      searchWorkflowNodesHelp: 'คุณลักษณะนี้ใช้ได้เฉพาะเมื่อดูเวิร์กโฟลว์เท่านั้น นําทางไปยังเวิร์กโฟลว์ก่อน',
      searchWorkflowNodesDesc: 'ค้นหาและข้ามไปยังโหนดในเวิร์กโฟลว์ปัจจุบันตามชื่อหรือประเภท',
      themeCategoryTitle: 'ธีม',
      languageCategoryTitle: 'ภาษา',
      runTitle: 'คำสั่ง',
      themeDark: 'ธีมมืด',
      languageChangeDesc: 'เปลี่ยนภาษา UI',
      themeSystem: 'ธีมระบบ',
      themeLight: 'ธีมสว่าง',
      runDesc: 'เรียกใช้คำสั่งอย่างรวดเร็ว (ธีม, ภาษา, ... )',
      themeDarkDesc: 'ใช้รูปลักษณ์เข้ม',
      themeCategoryDesc: 'เปลี่ยนธีมแอปพลิเคชัน',
      languageCategoryDesc: 'เปลี่ยนภาษาของอินเทอร์เฟซ',
      themeLightDesc: 'ใช้รูปลักษณ์ที่มีความสว่าง',
      themeSystemDesc: 'ติดตามรูปลักษณ์ของระบบปฏิบัติการของคุณ',
      slashDesc: 'ใช้คำสั่งเช่น /theme, /lang',
    },
    emptyState: {
      noPluginsFound: 'ไม่พบปลั๊กอิน',
      noAppsFound: 'ไม่พบแอป',
      noWorkflowNodesFound: 'ไม่พบโหนดเวิร์กโฟลว์',
      noKnowledgeBasesFound: 'ไม่พบฐานความรู้',
      tryDifferentTerm: 'ลองใช้คำค้นหาที่แตกต่างออกไปหรือลบตัวกรอง {{mode}}',
      trySpecificSearch: 'ลองใช้ {{shortcuts}} สำหรับการค้นหาเฉพาะ',
    },
    groups: {
      apps: 'แอปพลิเคชัน',
      knowledgeBases: 'ฐานความรู้',
      plugins: 'ปลั๊กอิน',
      workflowNodes: 'โหนดเวิร์กโฟลว์',
      commands: 'คำสั่ง',
    },
    searchTitle: 'ค้นหาอะไรก็ได้',
    searchFailed: 'การค้นหาล้มเหลว',
    useAtForSpecific: 'ใช้ @ สําหรับบางประเภท',
    noResults: 'ไม่พบผลลัพธ์',
    searchTemporarilyUnavailable: 'การค้นหาไม่พร้อมใช้งานชั่วคราว',
    someServicesUnavailable: 'บริการค้นหาบางบริการไม่พร้อมใช้งาน',
    clearToSearchAll: 'ล้าง @ เพื่อค้นหาทั้งหมด',
    searchPlaceholder: 'ค้นหาหรือพิมพ์ @ สำหรับคำสั่ง...',
    servicesUnavailableMessage: 'บริการค้นหาบางบริการอาจประสบปัญหา ลองอีกครั้งในอีกสักครู่',
    searching: 'กำลังค้นหา...',
    searchHint: 'เริ่มพิมพ์เพื่อค้นหาทุกอย่างได้ทันที',
    selectSearchType: 'เลือกสิ่งที่จะค้นหา',
    commandHint: 'พิมพ์ @ เพื่อเรียกดูตามหมวดหมู่',
    resultCount: '{{count}} ผลลัพธ์',
    resultCount_other: '{{count}} ผลลัพธ์',
    inScope: 'ใน {{scope}}s',
    noMatchingCommands: 'ไม่พบคำสั่งที่ตรงกัน',
    tryDifferentSearch: 'ลองใช้ข้อความค้นหาอื่น',
  },
}

export default translation
