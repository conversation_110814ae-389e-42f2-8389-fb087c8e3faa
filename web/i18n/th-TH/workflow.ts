const translation = {
  common: {
    undo: 'แก้',
    redo: 'พร้อม',
    editing: 'แก้ไข',
    autoSaved: 'บันทึกอัตโนมัติ',
    unpublished: 'ไม่ได้เผยแพร่',
    published: 'เผย แพร่',
    publish: 'ตีพิมพ์',
    update: 'อัพเดต',
    run: 'วิ่ง',
    running: 'กำลัง เรียก ใช้',
    inRunMode: 'ในโหมดเรียกใช้',
    inPreview: 'ในการแสดงตัวอย่าง',
    inPreviewMode: 'ในโหมดแสดงตัวอย่าง',
    preview: 'ดูตัวอย่าง',
    viewRunHistory: 'ดูประวัติการวิ่ง',
    runHistory: 'ประวัติการวิ่ง',
    goBackToEdit: 'กลับไปที่ตัวแก้ไข',
    conversationLog: 'บันทึกการสนทนา',
    features: 'หน้าตา',
    featuresDescription: 'ปรับปรุงประสบการณ์ผู้ใช้เว็บแอป',
    ImageUploadLegacyTip: 'ตอนนี้คุณสามารถสร้างตัวแปรชนิดไฟล์ในฟอร์มเริ่มต้นได้แล้ว เราจะไม่รองรับฟีเจอร์การอัปโหลดรูปภาพอีกต่อไปในอนาคต',
    fileUploadTip: 'ฟีเจอร์การอัปโหลดรูปภาพได้รับการอัปเกรดเป็นการอัปโหลดไฟล์',
    featuresDocLink: 'ศึกษาเพิ่มเติม',
    debugAndPreview: 'ดูตัวอย่าง',
    restart: 'เริ่มใหม่',
    currentDraft: 'ร่างปัจจุบัน',
    currentDraftUnpublished: 'ร่างปัจจุบันที่ไม่ได้เผยแพร่',
    latestPublished: 'เผยแพร่ล่าสุด',
    publishedAt: 'เผย แพร่',
    restore: 'ซ่อมแซม',
    runApp: 'เรียกใช้แอพ',
    batchRunApp: 'แอป Batch Run',
    accessAPIReference: 'การอ้างอิง API การเข้าถึง',
    embedIntoSite: 'ฝังลงในไซต์',
    addTitle: 'เพิ่มชื่อเรื่อง...',
    addDescription: 'เพิ่มคําอธิบาย...',
    noVar: 'ไม่มีตัวแปร',
    searchVar: 'ตัวแปรการค้นหา',
    variableNamePlaceholder: 'ชื่อตัวแปร',
    setVarValuePlaceholder: 'ตั้งค่าตัวแปร',
    needConnectTip: 'ขั้นตอนนี้ไม่ได้เชื่อมต่อกับสิ่งใด',
    maxTreeDepth: 'ขีดจํากัดสูงสุดของ {{depth}} โหนดต่อสาขา',
    workflowProcess: 'กระบวนการเวิร์กโฟลว์',
    notRunning: 'ยังไม่ได้ทํางาน',
    previewPlaceholder: 'ป้อนเนื้อหาในช่องด้านล่างเพื่อเริ่มแก้ไขข้อบกพร่องของแชทบอท',
    effectVarConfirm: {
      title: 'ลบตัวแปร',
      content: 'ตัวแปรนี้ใช้ในโหนดอื่น คุณยังต้องการลบออกหรือไม่?',
    },
    insertVarTip: 'กดปุ่ม \'/\' เพื่อแทรกอย่างรวดเร็ว',
    processData: 'ประมวลผลข้อมูล',
    input: 'อินพุต',
    output: 'ผลิตภัณฑ์',
    jinjaEditorPlaceholder: 'พิมพ์ \'/\' หรือ \'{\' เพื่อแทรกตัวแปร',
    viewOnly: 'ดูเท่านั้น',
    showRunHistory: 'แสดงประวัติการวิ่ง',
    enableJinja: 'เปิดใช้งานการสนับสนุนเทมเพลต Jinja',
    learnMore: 'ศึกษาเพิ่มเติม',
    copy: 'ลอก',
    duplicate: 'สำเนา',
    pasteHere: 'วางที่นี่',
    pointerMode: 'โหมดตัวชี้',
    handMode: 'โหมดมือ',
    model: 'แบบ',
    workflowAsTool: 'เวิร์กโฟลว์เป็นเครื่องมือ',
    configureRequired: 'กําหนดค่าที่จําเป็น',
    configure: 'กําหนดค่า',
    manageInTools: 'จัดการในเครื่องมือ',
    workflowAsToolTip: 'จําเป็นต้องมีการกําหนดค่าเครื่องมือใหม่หลังจากการอัปเดตเวิร์กโฟลว์',
    viewDetailInTracingPanel: 'ดูรายละเอียด',
    syncingData: 'ซิงค์ข้อมูลเพียงไม่กี่วินาที',
    importDSL: 'นําเข้า DSL',
    importDSLTip: 'ร่างปัจจุบันจะถูกเขียนทับ\nส่งออกเวิร์กโฟลว์เป็นข้อมูลสํารองก่อนนําเข้า',
    backupCurrentDraft: 'สํารองร่างปัจจุบัน',
    chooseDSL: 'เลือกไฟล์ DSL',
    overwriteAndImport: 'เขียนทับและนําเข้า',
    importFailure: 'นําเข้าล้มเหลว',
    importWarning: 'ความระมัดระวัง',
    importWarningDetails: 'ความแตกต่างของเวอร์ชัน DSL อาจส่งผลต่อคุณสมบัติบางอย่าง',
    importSuccess: 'นําเข้าสําเร็จ',
    parallelRun: 'วิ่งแบบขนาน',
    parallelTip: {
      click: {
        title: 'คลิก',
        desc: 'เพื่อเพิ่ม',
      },
      drag: {
        title: 'ลาก',
        desc: 'เพื่อเชื่อมต่อ',
      },
      limit: 'ความขนานถูกจํากัดไว้ที่ {{num}} สาขา',
      depthLimit: 'ขีดจํากัดเลเยอร์ซ้อนแบบขนานของ {{num}} เลเยอร์',
    },
    disconnect: 'ยก เลิก',
    jumpToNode: 'ข้ามไปยังโหนดนี้',
    addParallelNode: 'เพิ่มโหนดขนาน',
    parallel: 'ขนาน',
    branch: 'กิ่ง',
    openInExplore: 'เปิดใน Explore',
    onFailure: 'เมื่อล้มเหลว',
    addFailureBranch: 'เพิ่มสาขา Fail',
    loadMore: 'โหลดเวิร์กโฟลว์เพิ่มเติม',
    noHistory: 'ไม่มีประวัติ',
    versionHistory: 'ประวัติรุ่น',
    exportPNG: 'ส่งออกเป็น PNG',
    exportJPEG: 'ส่งออกเป็น JPEG',
    publishUpdate: 'เผยแพร่การอัปเดต',
    exitVersions: 'ออกเวอร์ชัน',
    exportImage: 'ส่งออกภาพ',
    exportSVG: 'ส่งออกเป็น SVG',
    needAnswerNode: 'ต้องเพิ่มโหนดคำตอบ',
    addBlock: 'เพิ่มโนด',
    needEndNode: 'ต้องเพิ่มโหนดจบ',
    tagBound: 'จำนวนแอปพลิเคชันที่ใช้แท็กนี้',
    currentWorkflow: 'เวิร์กโฟลว์ปัจจุบัน',
    currentView: 'ปัจจุบัน View',
  },
  env: {
    envPanelTitle: 'ตัวแปรสภาพแวดล้อม',
    envDescription: 'ตัวแปรสภาพแวดล้อมสามารถใช้เพื่อจัดเก็บข้อมูลส่วนตัวและข้อมูลประจําตัวได้ เป็นแบบอ่านอย่างเดียวและสามารถแยกออกจากไฟล์ DSL ระหว่างการส่งออก',
    envPanelButton: 'เพิ่มตัวแปร',
    modal: {
      title: 'เพิ่มตัวแปรสภาพแวดล้อม',
      editTitle: 'แก้ไขตัวแปรสภาพแวดล้อม',
      type: 'ประเภท',
      name: 'ชื่อ',
      namePlaceholder: 'ชื่อ env',
      value: 'ค่า',
      valuePlaceholder: 'ค่า env',
      secretTip: 'ใช้เพื่อกําหนดข้อมูลหรือข้อมูลที่ละเอียดอ่อน โดยมีการตั้งค่า DSL ที่กําหนดค่าไว้เพื่อป้องกันการรั่วไหล',
      description: 'คำอธิบาย',
      descriptionPlaceholder: 'อธิบายตัวแปร',
    },
    export: {
      title: 'ส่งออกตัวแปรสภาพแวดล้อม Secret หรือไม่',
      checkbox: 'ส่งออกค่าข้อมูลลับ',
      ignore: 'ส่งออก DSL',
      export: 'ส่งออก DSL ด้วยค่าลับ',
    },
  },
  chatVariable: {
    panelTitle: 'ตัวแปรการสนทนา',
    panelDescription: 'ตัวแปรการสนทนาใช้เพื่อจัดเก็บข้อมูลแบบโต้ตอบที่ LLM จําเป็นต้องจดจํา รวมถึงประวัติการสนทนา ไฟล์ที่อัปโหลด การตั้งค่าของผู้ใช้ พวกเขาอ่าน-เขียน',
    docLink: 'เยี่ยมชมเอกสารของเราเพื่อเรียนรู้เพิ่มเติม',
    button: 'เพิ่มตัวแปร',
    modal: {
      title: 'เพิ่มตัวแปรการสนทนา',
      editTitle: 'แก้ไขตัวแปรการสนทนา',
      name: 'ชื่อ',
      namePlaceholder: 'ชื่อตัวแปร',
      type: 'ประเภท',
      value: 'ค่าเริ่มต้น',
      valuePlaceholder: 'ค่าเริ่มต้น เว้นว่างไว้เพื่อไม่ให้ตั้งค่า',
      description: 'คำอธิบาย',
      descriptionPlaceholder: 'อธิบายตัวแปร',
      editInJSON: 'แก้ไขใน JSON',
      oneByOne: 'เพิ่มทีละรายการ',
      editInForm: 'แก้ไขในแบบฟอร์ม',
      arrayValue: 'ค่า',
      addArrayValue: 'เพิ่มมูลค่า',
      objectKey: 'กุญแจ',
      objectType: 'ประเภท',
      objectValue: 'ค่าเริ่มต้น',
    },
    storedContent: 'เนื้อหาที่เก็บไว้',
    updatedAt: 'อัพเดทเมื่อ',
  },
  changeHistory: {
    title: 'ประวัติการเปลี่ยนแปลง',
    placeholder: 'คุณยังไม่ได้เปลี่ยนแปลงอะไรเลย',
    clearHistory: 'ล้างประวัติ',
    hint: 'คนอินเดีย',
    hintText: 'การดําเนินการแก้ไขของคุณจะถูกติดตามในประวัติการเปลี่ยนแปลง ซึ่งจะถูกจัดเก็บไว้ในอุปกรณ์ของคุณตลอดระยะเวลาของเซสชันนี้ ประวัตินี้จะถูกล้างเมื่อคุณออกจากตัวแก้ไข',
    stepBackward_one: '{{count}} ถอยหลัง',
    stepBackward_other: '{{count}} ถอยหลัง',
    stepForward_one: '{{count}} ก้าวไปข้างหน้า',
    stepForward_other: '{{count}} ก้าวไปข้างหน้า',
    sessionStart: 'เริ่มเซสชัน',
    currentState: 'สถานะปัจจุบัน',
    noteAdd: 'เพิ่มหมายเหตุ',
    noteChange: 'เปลี่ยนหมายเหตุ',
    noteDelete: 'ลบโน้ต',
    nodeDelete: 'โหนดถูกลบแล้ว',
    nodeDescriptionChange: 'คำอธิบายของโหนดถูกเปลี่ยน',
    nodeDragStop: 'โหนดถูกย้าย',
    edgeDelete: 'เชื่อมต่อ Node ขาดหาย',
    nodeTitleChange: 'ชื่อโหนดเปลี่ยน',
    nodeAdd: 'เพิ่มโนด',
    nodeChange: 'โหนดเปลี่ยนแปลง',
    nodeResize: 'ขนาดของโหนดถูกปรับขนาด',
    nodeConnect: 'เชื่อมต่อ Node',
    nodePaste: 'โนดที่วางไว้',
  },
  errorMsg: {
    fieldRequired: '{{field}} เป็นสิ่งจําเป็น',
    rerankModelRequired: 'ก่อนเปิด Rerank Model โปรดยืนยันว่าได้กําหนดค่าโมเดลสําเร็จในการตั้งค่า',
    authRequired: 'ต้องได้รับอนุญาต',
    invalidJson: '{{field}} เป็น JSON ไม่ถูกต้อง',
    fields: {
      variable: 'ชื่อตัวแปร',
      variableValue: 'ค่าตัวแปร',
      code: 'รหัส',
      model: 'แบบ',
      rerankModel: 'จัดอันดับโมเดลใหม่',
      visionVariable: 'ตัวแปรวิสัยทัศน์',
    },
    invalidVariable: 'ตัวแปรไม่ถูกต้อง',
    noValidTool: '{{field}} ไม่ได้เลือกเครื่องมือที่ถูกต้อง',
    toolParameterRequired: '{{field}}: พารามิเตอร์ [{{param}}] เป็นสิ่งจําเป็น',
  },
  singleRun: {
    testRun: 'ทดสอบการทํางาน',
    startRun: 'เริ่มวิ่ง',
    running: 'กำลัง เรียก ใช้',
    testRunIteration: 'การทดสอบการทําซ้ํา',
    back: 'ย้อนกลับ',
    iteration: 'เกิด ซ้ำ',
    loop: 'ลูป',
  },
  tabs: {
    'searchTool': 'เครื่องมือค้นหา',
    'tools': 'เครื่อง มือ',
    'allTool': 'ทั้งหมด',
    'customTool': 'ธรรมเนียม',
    'workflowTool': 'เวิร์กโฟลว์',
    'question-understand': 'คําถาม: เข้าใจ',
    'logic': 'ตรรกวิทยา',
    'transform': 'แปลง',
    'utilities': 'สาธารณูปโภค',
    'noResult': 'ไม่พบการจับคู่',
    'agent': 'กลยุทธ์ตัวแทน',
    'plugin': 'ปลั๊กอิน',
    'searchBlock': 'ค้นหาโหนด',
    'blocks': 'โหนด',
    'allAdded': 'ทั้งหมดที่เพิ่มเข้ามา',
    'addAll': 'เพิ่มทั้งหมด',
  },
  blocks: {
    'start': 'เริ่ม',
    'end': 'ปลาย',
    'answer': 'ตอบ',
    'llm': 'นิติศาสตราจารย์',
    'knowledge-retrieval': 'การดึงความรู้',
    'question-classifier': 'ตัวจําแนกคําถาม',
    'if-else': 'ถ้า/อื่น',
    'code': 'รหัส',
    'template-transform': 'แม่ แบบ',
    'http-request': 'คําขอ HTTP',
    'variable-assigner': 'ตัวรวบรวมตัวแปร',
    'variable-aggregator': 'ตัวรวบรวมตัวแปร',
    'assigner': 'ตัวกําหนดตัวแปร',
    'iteration-start': 'เริ่มการทําซ้ํา',
    'iteration': 'เกิด ซ้ำ',
    'parameter-extractor': 'ตัวแยกพารามิเตอร์',
    'document-extractor': 'ตัวแยกเอกสาร',
    'list-operator': 'ตัวดําเนินการรายการ',
    'agent': 'ตัวแทน',
    'loop': 'ลูป',
    'loop-start': 'เริ่มลูป',
    'loop-end': 'ออกจากลูป',
  },
  blocksAbout: {
    'start': 'กําหนดพารามิเตอร์เริ่มต้นสําหรับการเปิดใช้เวิร์กโฟลว์',
    'end': 'กําหนดชนิดสิ้นสุดและผลลัพธ์ของเวิร์กโฟลว์',
    'answer': 'กําหนดเนื้อหาการตอบกลับของการสนทนาแชท',
    'llm': 'การเรียกใช้โมเดลภาษาขนาดใหญ่เพื่อตอบคําถามหรือประมวลผลภาษาธรรมชาติ',
    'knowledge-retrieval': 'ช่วยให้คุณสามารถสอบถามเนื้อหาข้อความที่เกี่ยวข้องกับคําถามของผู้ใช้จากความรู้',
    'question-classifier': 'กําหนดเงื่อนไขการจําแนกประเภทของคําถามของผู้ใช้ LLM สามารถกําหนดความคืบหน้าของการสนทนาตามคําอธิบายการจําแนกประเภท',
    'if-else': 'ช่วยให้คุณสามารถแบ่งเวิร์กโฟลว์ออกเป็นสองสาขาตามเงื่อนไข if/else',
    'code': 'เรียกใช้โค้ด Python หรือ NodeJS เพื่อใช้ตรรกะที่กําหนดเอง',
    'template-transform': 'แปลงข้อมูลเป็นสตริงโดยใช้ไวยากรณ์เทมเพลต Jinja',
    'http-request': 'อนุญาตให้ส่งคําขอเซิร์ฟเวอร์ผ่านโปรโตคอล HTTP',
    'variable-assigner': 'รวมตัวแปรหลายสาขาเป็นตัวแปรเดียวสําหรับการกําหนดค่าแบบรวมของโหนดดาวน์สตรีม',
    'assigner': 'โหนดการกําหนดตัวแปรใช้สําหรับกําหนดค่าให้กับตัวแปรที่เขียนได้ (เช่นตัวแปรการสนทนา)',
    'variable-aggregator': 'รวมตัวแปรหลายสาขาเป็นตัวแปรเดียวสําหรับการกําหนดค่าแบบรวมของโหนดดาวน์สตรีม',
    'iteration': 'ดําเนินการหลายขั้นตอนกับวัตถุรายการจนกว่าจะส่งออกผลลัพธ์ทั้งหมด',
    'parameter-extractor': 'ใช้ LLM เพื่อแยกพารามิเตอร์ที่มีโครงสร้างจากภาษาธรรมชาติสําหรับการเรียกใช้เครื่องมือหรือคําขอ HTTP',
    'document-extractor': 'ใช้เพื่อแยกวิเคราะห์เอกสารที่อัปโหลดเป็นเนื้อหาข้อความที่ LLM เข้าใจได้ง่าย',
    'list-operator': 'ใช้เพื่อกรองหรือจัดเรียงเนื้อหาอาร์เรย์',
    'agent': 'การเรียกใช้โมเดลภาษาขนาดใหญ่เพื่อตอบคําถามหรือประมวลผลภาษาธรรมชาติ',
    'loop': 'ดำเนินการลูปของตรรกะจนกว่าจะถึงเงื่อนไขการสิ้นสุดหรือตรงตามจำนวนลูปสูงสุดที่กำหนด.',
    'loop-end': 'เทียบเท่ากับ "break" โหนดนี้ไม่มีรายการการกำหนดค่า เมื่อร่างกายของลูปถึงโหนดนี้ ลูปจะสิ้นสุดลง.',
  },
  operator: {
    zoomIn: 'ซูมเข้า',
    zoomOut: 'ซูมออก',
    zoomTo50: 'ซูมไปที่ 50%',
    zoomTo100: 'ซูมไปที่ 100%',
    zoomToFit: 'ซูมให้พอดี',
    alignBottom: 'ด้านล่าง',
    alignCenter: 'ศูนย์กลาง',
    alignMiddle: 'กลาง',
    horizontal: 'แนวนอน',
    vertical: 'แนวตั้ง',
    alignTop: 'ด้านบน',
    distributeVertical: 'ระยะห่างแนวตั้ง',
    alignLeft: 'ซ้าย',
    selectionAlignment: 'การจัดตําแหน่งการเลือก',
    distributeHorizontal: 'ระยะห่างแนวนอน',
    alignRight: 'ขวา',
    alignNodes: 'จัดตําแหน่งโหนด',
  },
  panel: {
    userInputField: 'ฟิลด์ป้อนข้อมูลของผู้ใช้',
    helpLink: 'ลิงค์ช่วยเหลือ',
    about: 'ประมาณ',
    createdBy: 'สร้างโดย',
    nextStep: 'ขั้นตอนถัดไป',
    runThisStep: 'เรียกใช้ขั้นตอนนี้',
    checklist: 'ตรวจ สอบ',
    checklistTip: 'ตรวจสอบให้แน่ใจว่าปัญหาทั้งหมดได้รับการแก้ไขแล้วก่อนที่จะเผยแพร่',
    checklistResolved: 'ปัญหาทั้งหมดได้รับการแก้ไขแล้ว',
    change: 'เปลี่ยน',
    optional: '(ไม่บังคับ)',
    moveToThisNode: 'ย้ายไปที่โหนดนี้',
    organizeBlocks: 'จัดระเบียบโหนด',
    addNextStep: 'เพิ่มขั้นตอนถัดไปในกระบวนการทำงานนี้',
    changeBlock: 'เปลี่ยนโหนด',
    selectNextStep: 'เลือกขั้นตอนถัดไป',
    minimize: 'ออกจากโหมดเต็มหน้าจอ',
    maximize: 'เพิ่มประสิทธิภาพผ้าใบ',
  },
  nodes: {
    common: {
      outputVars: 'ตัวแปรเอาต์พุต',
      insertVarTip: 'แทรกตัวแปร',
      memory: {
        memory: 'ความจำ',
        memoryTip: 'การตั้งค่าหน่วยความจําแชท',
        windowSize: 'ขนาดหน้าต่าง',
        conversationRoleName: 'ชื่อบทบาทการสนทนา',
        user: 'คํานําหน้าผู้ใช้',
        assistant: 'คํานําหน้าผู้ช่วย',
      },
      memories: {
        title: 'ความ ทรง จำ',
        tip: 'ความทรงจําการแชท',
        builtIn: 'ในตัว',
      },
      errorHandle: {
        none: {
          title: 'ไม่มีใคร',
          desc: 'โหนดจะหยุดทํางานหากเกิดข้อยกเว้นและไม่ได้รับการจัดการ',
        },
        defaultValue: {
          title: 'ค่าเริ่มต้น',
          desc: 'เมื่อเกิดข้อผิดพลาด ให้ระบุเนื้อหาเอาต์พุตแบบคงที่',
          tip: 'เมื่อเกิดข้อผิดพลาด จะส่งคืนค่าต่ํากว่า',
          inLog: 'ข้อยกเว้นโหนดส่งออกตามค่าเริ่มต้น',
          output: 'ค่าเริ่มต้นเอาต์พุต',
        },
        failBranch: {
          title: 'สาขาล้มเหลว',
          desc: 'เมื่อเกิดข้อผิดพลาด จะดําเนินการสาขาข้อยกเว้น',
          customize: 'ไปที่พื้นที่ทํางานเพื่อปรับแต่งตรรกะสาขาที่ล้มเหลว',
          customizeTip: 'เมื่อเปิดใช้งานสาขาล้มเหลว ข้อยกเว้นที่โหนดโยนจะไม่ยุติกระบวนการ แต่จะเรียกใช้สาขาล้มเหลวที่กําหนดไว้ล่วงหน้าโดยอัตโนมัติ ช่วยให้คุณระบุข้อความแสดงข้อผิดพลาด รายงาน การแก้ไข หรือข้ามการดําเนินการได้อย่างยืดหยุ่น',
          inLog: 'ข้อยกเว้นโหนดจะดําเนินการสาขาที่ล้มเหลวโดยอัตโนมัติ เอาต์พุตโหนดจะส่งคืนประเภทข้อผิดพลาดและข้อความแสดงข้อผิดพลาดและส่งต่อไปยังดาวน์สตรีม',
        },
        partialSucceeded: {
          tip: 'มีโหนด {{num}} ในกระบวนการที่ทํางานผิดปกติ โปรดไปที่การติดตามเพื่อตรวจสอบบันทึก',
        },
        title: 'การจัดการข้อผิดพลาด',
        tip: 'กลยุทธ์การจัดการข้อยกเว้น ทริกเกอร์เมื่อโหนดพบข้อยกเว้น',
      },
      retry: {
        retry: 'ลอง',
        retryOnFailure: 'ลองใหม่เมื่อล้มเหลว',
        maxRetries: 'การลองซ้ําสูงสุด',
        retryInterval: 'ช่วงเวลาลองใหม่',
        retryTimes: 'ลอง {{times}} ครั้งเมื่อล้มเหลว',
        retrying: 'กําลังลองซ้ํา...',
        retrySuccessful: 'ลองใหม่สําเร็จ',
        retryFailed: 'ลองใหม่ล้มเหลว',
        retryFailedTimes: '{{times}} การลองซ้ําล้มเหลว',
        times: 'ครั้ง',
        retries: '{{num}} ลอง',
        ms: 'นางสาว',
      },
      typeSwitch: {
        input: 'ค่าป้อนข้อมูล',
        variable: 'ใช้ตัวแปร',
      },
    },
    start: {
      required: 'ต้องระบุ',
      inputField: 'ฟิลด์อินพุต',
      builtInVar: 'ตัวแปรในตัว',
      outputVars: {
        query: 'การป้อนข้อมูลของผู้ใช้',
        memories: {
          des: 'ประวัติการสนทนา',
          type: 'ประเภทข้อความ',
          content: 'เนื้อหาข้อความ',
        },
        files: 'รายการไฟล์',
      },
      noVarTip: 'ตั้งค่าอินพุตที่สามารถใช้ในเวิร์กโฟลว์',
    },
    end: {
      outputs: 'เอาต์ พุ ต',
      output: {
        type: 'ประเภทเอาต์พุต',
        variable: 'ตัวแปรเอาต์พุต',
      },
      type: {
        'none': 'ไม่มีใคร',
        'plain-text': 'ข้อความธรรมดา',
        'structured': 'โครง สร้าง',
      },
    },
    answer: {
      answer: 'ตอบ',
      outputVars: 'ตัวแปรเอาต์พุต',
    },
    llm: {
      model: 'แบบ',
      variables: 'ตัว แปร',
      context: 'บริบท',
      contextTooltip: 'คุณสามารถนําเข้าความรู้เป็นบริบทได้',
      notSetContextInPromptTip: 'หากต้องการเปิดใช้งานคุณสมบัติบริบท โปรดกรอกตัวแปรบริบทใน PROMPT',
      prompt: 'พร้อมท์',
      roleDescription: {
        system: 'ให้คําแนะนําระดับสูงสําหรับการสนทนา',
        user: 'ให้คําแนะนํา แบบสอบถาม หรือการป้อนข้อมูลตามข้อความใดๆ ไปยังแบบจําลอง',
        assistant: 'การตอบสนองของโมเดลตามข้อความของผู้ใช้',
      },
      addMessage: 'เพิ่มข้อความ',
      vision: 'การมองเห็น',
      files: 'แฟ้ม',
      resolution: {
        name: 'มติ',
        high: 'สูง',
        low: 'ต่ํา',
      },
      outputVars: {
        output: 'สร้างเนื้อหา',
        usage: 'ข้อมูลการใช้งานรุ่น',
      },
      singleRun: {
        variable: 'ตัวแปร',
      },
      sysQueryInUser: 'sys.query ในข้อความผู้ใช้เป็นสิ่งจําเป็น',
      jsonSchema: {
        warningTips: {
          saveSchema: 'กรุณาแก้ไขฟิลด์ปัจจุบันให้เสร็จก่อนที่จะบันทึกสคีมา',
        },
        apply: 'สมัคร',
        resetDefaults: 'รีเซ็ต',
        generate: 'สร้าง',
        import: 'นำเข้าจาก JSON',
        descriptionPlaceholder: 'เพิ่มคำอธิบาย',
        instruction: 'คำแนะนำ',
        generating: 'กำลังสร้าง JSON Schema...',
        resultTip: 'นี่คือผลลัพธ์ที่สร้างขึ้น หากคุณไม่พอใจ คุณสามารถกลับไปและแก้ไขคำสั่งของคุณได้',
        regenerate: 'สร้างใหม่',
        title: 'รูปแบบข้อมูลที่จัดระเบียบ',
        promptPlaceholder: 'โปรดอธิบาย JSON Schema ของคุณ...',
        generatedResult: 'ผลลัพธ์ที่สร้างขึ้น',
        generateJsonSchema: 'สร้าง JSON Schema',
        promptTooltip: 'แปลงคำอธิบายข้อความเป็นโครงสร้าง JSON Schema มาตรฐาน.',
        showAdvancedOptions: 'แสดงตัวเลือกขั้นสูง',
        addField: 'เพิ่มฟิลด์',
        back: 'กลับ',
        fieldNamePlaceholder: 'ชื่อฟิลด์',
        generationTip: 'คุณสามารถใช้ภาษาธรรมชาติในการสร้าง JSON Schema ได้อย่างรวดเร็ว.',
        doc: 'เรียนรู้เพิ่มเติมเกี่ยวกับผลลัพธ์ที่มีโครงสร้าง',
        addChildField: 'เพิ่มฟิลด์เด็ก',
        stringValidations: 'การตรวจสอบสตริง',
        required: 'จำเป็นต้องใช้',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'ตัวแปรแบบสอบถาม',
      knowledge: 'ความรู้',
      outputVars: {
        output: 'การดึงข้อมูลที่แบ่งส่วน',
        content: 'เนื้อหาที่แบ่งกลุ่ม',
        title: 'ชื่อแบ่งส่วน',
        icon: 'ไอคอนแบ่งส่วน',
        url: 'URL ที่แบ่งกลุ่ม',
        metadata: 'ข้อมูลเมตาอื่นๆ',
      },
      metadata: {
        options: {
          disabled: {
            title: 'คนพิการ',
            subTitle: 'ไม่ได้เปิดใช้งานการกรองข้อมูลเมตา',
          },
          automatic: {
            desc: 'สร้างเงื่อนไขการกรองข้อมูลเมตาโดยอัตโนมัติตามตัวแปรค้นหา',
            title: 'อัตโนมัติ',
            subTitle: 'สร้างเงื่อนไขการกรองข้อมูลเมตาโดยอัตโนมัติตามการค้นหาของผู้ใช้',
          },
          manual: {
            subTitle: 'เพิ่มเงื่อนไขการกรองข้อมูลเมตาด้วยตนเอง',
            title: 'คู่มือ',
          },
        },
        panel: {
          conditions: 'เงื่อนไข',
          search: 'ค้นหาข้อมูลเมตา',
          add: 'เพิ่มเงื่อนไข',
          datePlaceholder: 'เลือกเวลา...',
          title: 'เงื่อนไขการกรองข้อมูลเมตา',
          select: 'เลือกตัวแปร...',
          placeholder: 'ใส่ค่า',
        },
        title: 'การกรองข้อมูลเมตา',
        tip: 'การกรองข้อมูลเมตาดาต้าเป็นกระบวนการที่ใช้คุณลักษณะของเมตาดาต้า (เช่น แท็ก หมวดหมู่ หรือสิทธิการเข้าถึง) เพื่อปรับแต่งและควบคุมการดึงข้อมูลที่เกี่ยวข้องภายในระบบ.',
      },
    },
    http: {
      inputVars: 'ตัวแปรอินพุต',
      api: 'เอพีไอ',
      apiPlaceholder: 'ป้อน URL พิมพ์ \'/\' แทรกตัวแปร',
      extractListPlaceholder: 'ป้อนดัชนีรายการพิมพ์ \'/\' แทรกตัวแปร',
      notStartWithHttp: 'API ควรขึ้นต้นด้วย http:// หรือ https://',
      key: 'กุญแจ',
      type: 'ประเภท',
      value: 'ค่า',
      bulkEdit: 'แก้ไขจํานวนมาก',
      keyValueEdit: 'การแก้ไขคีย์-ค่า',
      headers: 'หัว กระดาษ',
      params: 'พารามิเตอร์',
      body: 'ร่างกาย',
      binaryFileVariable: 'ตัวแปรไฟล์ไบนารี',
      outputVars: {
        body: 'เนื้อหาการตอบกลับ',
        statusCode: 'รหัสสถานะการตอบกลับ',
        headers: 'JSON รายการส่วนหัวการตอบสนอง',
        files: 'รายการไฟล์',
      },
      authorization: {
        'authorization': 'การอนุญาต',
        'authorizationType': 'ประเภทการอนุญาต',
        'no-auth': 'ไม่มีใคร',
        'api-key': 'คีย์ API',
        'auth-type': 'ประเภทการรับรองความถูกต้อง',
        'basic': 'พื้นฐาน',
        'bearer': 'ผู้ถือ',
        'custom': 'ธรรมเนียม',
        'api-key-title': 'คีย์ API',
        'header': 'หัว ข้อ',
      },
      insertVarPlaceholder: 'พิมพ์ \'/\' เพื่อแทรกตัวแปร',
      timeout: {
        title: 'หมดเวลา',
        connectLabel: 'หมดเวลาการเชื่อมต่อ',
        connectPlaceholder: 'ป้อนการหมดเวลาการเชื่อมต่อเป็นวินาที',
        readLabel: 'หมดเวลาการอ่าน',
        readPlaceholder: 'ป้อนหมดเวลาการอ่านเป็นวินาที',
        writeLabel: 'หมดเวลาการเขียน',
        writePlaceholder: 'ป้อนหมดเวลาการเขียนเป็นวินาที',
      },
      curl: {
        title: 'นําเข้าจาก cURL',
        placeholder: 'วางสตริง cURL ที่นี่',
      },
      verifySSL: {
        title: 'ตรวจสอบใบรับรอง SSL',
        warningTooltip: 'การปิดการตรวจสอบ SSL ไม่แนะนำให้ใช้ในสภาพแวดล้อมการผลิต ควรใช้เฉพาะในระหว่างการพัฒนาหรือการทดสอบเท่านั้น เนื่องจากจะทำให้การเชื่อมต่อมีความเสี่ยงต่อภัยคุกคามด้านความปลอดภัย เช่น การโจมตีแบบ Man-in-the-middle.',
      },
    },
    code: {
      inputVars: 'ตัวแปรอินพุต',
      outputVars: 'ตัวแปรเอาต์พุต',
      advancedDependencies: 'การพึ่งพาขั้นสูง',
      advancedDependenciesTip: 'เพิ่มการพึ่งพาที่โหลดไว้ล่วงหน้าซึ่งใช้เวลามากขึ้นในการใช้หรือไม่ใช่ค่าเริ่มต้นในตัวที่นี่',
      searchDependencies: 'การพึ่งพาการค้นหา',
      syncFunctionSignature: 'ซิงก์ลายเซ็นฟังก์ชันให้ตรงกับโค้ด',
    },
    templateTransform: {
      inputVars: 'ตัวแปรอินพุต',
      code: 'รหัส',
      codeSupportTip: 'รองรับเฉพาะ Jinja2',
      outputVars: {
        output: 'เนื้อหาที่แปลงโฉม',
      },
    },
    ifElse: {
      if: 'ถ้า',
      else: 'อื่น',
      elseDescription: 'ใช้เพื่อกําหนดตรรกะที่ควรดําเนินการเมื่อไม่ตรงตามเงื่อนไข if',
      and: 'และ',
      or: 'หรือ',
      operator: 'ผู้ปฏิบัติการ',
      notSetVariable: 'โปรดตั้งค่าตัวแปรก่อน',
      comparisonOperator: {
        'contains': 'ประกอบ ด้วย',
        'not contains': 'ไม่มี',
        'start with': 'เริ่มต้นด้วย',
        'end with': 'ลงท้ายด้วย',
        'is': 'คือ',
        'is not': 'ไม่ใช่',
        'empty': 'ว่างเปล่า',
        'not empty': 'ไม่ว่างเปล่า',
        'null': 'เป็นโมฆะ',
        'not null': 'ไม่เป็นโมฆะ',
        'in': 'ใน',
        'not in': 'ไม่อยู่ใน',
        'all of': 'ทั้งหมด',
        'exists': 'อยู่',
        'not exists': 'ไม่มีอยู่จริง',
        'before': 'ก่อน',
        'after': 'หลังจากนั้น',
      },
      optionName: {
        image: 'ภาพ',
        doc: 'เอกสาร',
        audio: 'เสียง',
        video: 'วีดิทัศน์',
        localUpload: 'อัปโหลดในเครื่อง',
        url: 'URL',
      },
      enterValue: 'ป้อนค่า',
      addCondition: 'เพิ่มเงื่อนไข',
      conditionNotSetup: 'เงื่อนไข NOT ตั้งค่า',
      selectVariable: 'เลือกตัวแปร...',
      addSubVariable: 'ตัวแปรย่อย',
      select: 'เลือก',
    },
    variableAssigner: {
      title: 'กําหนดตัวแปร',
      outputType: 'ประเภทเอาต์พุต',
      varNotSet: 'ไม่ได้ตั้งค่าตัวแปร',
      noVarTip: 'เพิ่มตัวแปรที่จะกําหนด',
      type: {
        string: 'เชือก',
        number: 'เลข',
        object: 'วัตถุ',
        array: 'อาร์เรย์',
      },
      aggregationGroup: 'กลุ่มการรวม',
      aggregationGroupTip: 'การเปิดใช้งานคุณลักษณะนี้ช่วยให้ตัวรวบรวมตัวแปรสามารถรวมชุดตัวแปรหลายชุดได้',
      addGroup: 'เพิ่มกลุ่ม',
      outputVars: {
        varDescribe: '{{groupName}} เอาต์พุต',
      },
      setAssignVariable: 'ตั้งค่าตัวแปรกําหนด',
    },
    assigner: {
      'assignedVariable': 'ตัวแปรที่กําหนด',
      'writeMode': 'โหมดเขียน',
      'writeModeTip': 'โหมดผนวก: ใช้ได้กับตัวแปรอาร์เรย์เท่านั้น',
      'over-write': 'เขียน ทับ',
      'append': 'ผนวก',
      'plus': 'บวก',
      'clear': 'ใส',
      'setVariable': 'ตั้งค่าตัวแปร',
      'variable': 'ตัวแปร',
      'operations': {
        'set': 'ชุด',
        'append': 'ผนวก',
        '-=': '-=',
        '*=': '*=',
        'overwrite': 'เขียน ทับ',
        'extend': 'ขยาย',
        'title': 'การผ่าตัด',
        'clear': 'ใส',
        'over-write': 'เขียน ทับ',
        '+=': '+=',
        '/=': '/=',
        'remove-last': 'ลบสุดท้าย',
        'remove-first': 'ลบอันดับแรก',
      },
      'noAssignedVars': 'ไม่มีตัวแปรที่กําหนด',
      'selectAssignedVariable': 'เลือกตัวแปรที่กําหนด...',
      'variables': 'ตัว แปร',
      'varNotSet': 'ตัวแปรไม่ได้ตั้งค่า',
      'assignedVarsDescription': 'ตัวแปรที่กําหนดต้องเป็นตัวแปรที่เขียนได้ เช่น ตัวแปรการสนทนา',
      'noVarTip': 'คลิกปุ่ม "+" เพื่อเพิ่มตัวแปร',
      'setParameter': 'ตั้งค่าพารามิเตอร์...',
    },
    tool: {
      inputVars: 'ตัวแปรอินพุต',
      outputVars: {
        text: 'เนื้อหาที่สร้างขึ้นด้วยเครื่องมือ',
        files: {
          title: 'ไฟล์ที่สร้างขึ้นด้วยเครื่องมือ',
          type: 'ประเภทการสนับสนุน ตอนนี้รองรับเฉพาะรูปภาพ',
          transfer_method: 'วิธีการโอน ค่าเป็น remote_url หรือ local_file',
          url: 'URL ของรูปภาพ',
          upload_file_id: 'อัปโหลดรหัสไฟล์',
        },
        json: 'เครื่องมือสร้าง JSON',
      },
      authorize: 'อนุญาต',
      insertPlaceholder2: 'แทรกตัวแปร',
      insertPlaceholder1: 'พิมพ์หรือลงทะเบียน',
      settings: 'การตั้งค่า',
    },
    questionClassifiers: {
      model: 'แบบ',
      inputVars: 'ตัวแปรอินพุต',
      outputVars: {
        className: 'ชื่อคลาส',
        usage: 'ข้อมูลการใช้งานรุ่น',
      },
      class: 'ประเภท',
      classNamePlaceholder: 'เขียนชื่อชั้นเรียนของคุณ',
      advancedSetting: 'การตั้งค่าขั้นสูง',
      topicName: 'ชื่อหัวข้อ',
      topicPlaceholder: 'เขียนชื่อหัวข้อของคุณ',
      addClass: 'เพิ่มชั้นเรียน',
      instruction: 'การสอน',
      instructionTip: 'ป้อนคําแนะนําเพิ่มเติมเพื่อช่วยให้ตัวจําแนกคําถามเข้าใจวิธีจัดหมวดหมู่คําถามได้ดียิ่งขึ้น',
      instructionPlaceholder: 'เขียนคําแนะนําของคุณ',
    },
    parameterExtractor: {
      inputVar: 'ตัวแปรอินพุต',
      outputVars: {
        isSuccess: 'คือ Success เมื่อสําเร็จค่าคือ 1 เมื่อล้มเหลวค่าเป็น 0',
        errorReason: 'สาเหตุข้อผิดพลาด',
        usage: 'ข้อมูลการใช้งานรุ่น',
      },
      extractParameters: 'แยกพารามิเตอร์',
      importFromTool: 'นําเข้าจากเครื่องมือ',
      addExtractParameter: 'เพิ่มพารามิเตอร์การแยกข้อมูล',
      addExtractParameterContent: {
        name: 'ชื่อ',
        namePlaceholder: 'แยกชื่อพารามิเตอร์',
        type: 'ประเภท',
        typePlaceholder: 'แยกประเภทพารามิเตอร์',
        description: 'คำอธิบาย',
        descriptionPlaceholder: 'แยกคําอธิบายพารามิเตอร์',
        required: 'ต้องระบุ',
        requiredContent: 'Required ใช้เป็นข้อมูลอ้างอิงสําหรับการอนุมานแบบจําลองเท่านั้น และไม่ใช่สําหรับการตรวจสอบความถูกต้องของเอาต์พุตพารามิเตอร์ที่จําเป็น',
      },
      extractParametersNotSet: 'ไม่ได้ตั้งค่าพารามิเตอร์การแยกข้อมูล',
      instruction: 'การสอน',
      instructionTip: 'ป้อนคําแนะนําเพิ่มเติมเพื่อช่วยให้ตัวแยกพารามิเตอร์เข้าใจวิธีการแยกพารามิเตอร์',
      advancedSetting: 'การตั้งค่าขั้นสูง',
      reasoningMode: 'โหมดการให้เหตุผล',
      reasoningModeTip: 'คุณสามารถเลือกโหมดการให้เหตุผลที่เหมาะสมตามความสามารถของโมเดลในการตอบสนองต่อคําแนะนําสําหรับการเรียกใช้ฟังก์ชันหรือข้อความแจ้ง',
    },
    iteration: {
      deleteTitle: 'ลบโหนดการทําซ้ํา?',
      deleteDesc: 'การลบโหนดการวนซ้ําจะเป็นการลบโหนดย่อยทั้งหมด',
      input: 'อินพุต',
      output: 'ตัวแปรเอาต์พุต',
      iteration_one: '{{นับ}} เกิด ซ้ำ',
      iteration_other: '{{นับ}} เกิด ซ้ำ',
      currentIteration: 'การทําซ้ําปัจจุบัน',
      comma: ',',
      error_one: '{{นับ}} ความผิดพลาด',
      error_other: '{{นับ}} ข้อ ผิด พลาด',
      parallelMode: 'โหมดขนาน',
      parallelModeUpper: 'โหมดขนาน',
      parallelModeEnableTitle: 'เปิดใช้งานโหมดขนาน',
      parallelModeEnableDesc: 'ในโหมดขนาน งานภายในการทําซ้ําจะสนับสนุนการดําเนินการแบบขนาน คุณสามารถกําหนดค่านี้ได้ในแผงคุณสมบัติทางด้านขวา',
      parallelPanelDesc: 'ในโหมดขนาน งานในการวนซ้ําจะสนับสนุนการดําเนินการแบบขนาน',
      MaxParallelismTitle: 'ความขนานสูงสุด',
      MaxParallelismDesc: 'ความขนานสูงสุดใช้เพื่อควบคุมจํานวนงานที่ดําเนินการพร้อมกันในการทําซ้ําครั้งเดียว',
      errorResponseMethod: 'วิธีการตอบสนองข้อผิดพลาด',
      ErrorMethod: {
        operationTerminated: 'ยก เลิก',
        continueOnError: 'ดําเนินการต่อเมื่อเกิดข้อผิดพลาด',
        removeAbnormalOutput: 'ลบเอาต์พุตที่ผิดปกติ',
      },
      answerNodeWarningDesc: 'คําเตือนโหมดคู่ขนาน: โหนดคําตอบ การกําหนดตัวแปรการสนทนา และการดําเนินการอ่าน/เขียนแบบถาวรภายในการวนซ้ําอาจทําให้เกิดข้อยกเว้น',
    },
    note: {
      addNote: 'เพิ่มหมายเหตุ',
      editor: {
        placeholder: 'เขียนบันทึกของคุณ...',
        small: 'เล็ก',
        medium: 'ปานกลาง',
        large: 'ใหญ่',
        bold: 'กล้า',
        italic: 'ตัวเอียง',
        strikethrough: 'ขีดทับ',
        link: 'ลิงก์',
        openLink: 'เปิด',
        unlink: 'ยก เลิก',
        enterUrl: 'ป้อน URL...',
        invalidUrl: 'URL ไม่ถูกต้อง',
        bulletList: 'รายการสัญลักษณ์แสดงหัวข้อย่อย',
        showAuthor: 'แสดงผู้เขียน',
      },
    },
    docExtractor: {
      inputVar: 'ตัวแปรอินพุต',
      outputVars: {
        text: 'ข้อความที่แยกออกมา',
      },
      supportFileTypes: 'ประเภทไฟล์ที่รองรับ: {{types}}',
      learnMore: 'ศึกษาเพิ่มเติม',
    },
    listFilter: {
      inputVar: 'ตัวแปรอินพุต',
      filterCondition: 'เงื่อนไขการกรอง',
      filterConditionKey: 'คีย์เงื่อนไขตัวกรอง',
      extractsCondition: 'แยกรายการ N',
      filterConditionComparisonOperator: 'ตัวดําเนินการเปรียบเทียบเงื่อนไขตัวกรอง',
      filterConditionComparisonValue: 'ค่าเงื่อนไขตัวกรอง',
      selectVariableKeyPlaceholder: 'เลือกคีย์ตัวแปรย่อย',
      limit: 'ด้านบน N',
      orderBy: 'สั่งซื้อโดย',
      asc: 'เอเอสซี',
      desc: 'สูง สุด',
      outputVars: {
        result: 'กรองผลลัพธ์',
        first_record: 'บันทึกแรก',
        last_record: 'บันทึกล่าสุด',
      },
    },
    agent: {
      strategy: {
        label: 'กลยุทธ์ตัวแทน',
        tooltip: 'กลยุทธ์ Agentic ที่แตกต่างกันกําหนดวิธีที่ระบบวางแผนและดําเนินการเรียกใช้เครื่องมือหลายขั้นตอน',
        configureTipDesc: 'หลังจากกําหนดค่ากลยุทธ์ตัวแทนโหนดนี้จะโหลดการกําหนดค่าที่เหลือโดยอัตโนมัติ กลยุทธ์จะส่งผลต่อกลไกการให้เหตุผลของเครื่องมือหลายขั้นตอน',
        configureTip: 'โปรดกําหนดค่ากลยุทธ์เอเจนต์',
        searchPlaceholder: 'กลยุทธ์ตัวแทนการค้นหา',
        selectTip: 'เลือกกลยุทธ์ตัวแทน',
        shortLabel: 'ยุทธศาสตร์',
      },
      pluginInstaller: {
        installing: 'ติด ตั้ง',
        install: 'ติดตั้ง',
      },
      modelNotInMarketplace: {
        desc: 'โมเดลนี้ติดตั้งจากที่เก็บในเครื่องหรือ GitHub กรุณาใช้หลังการติดตั้ง',
        title: 'ไม่ได้ติดตั้งรุ่น',
        manageInPlugins: 'จัดการในปลั๊กอิน',
      },
      modelNotSupport: {
        descForVersionSwitch: 'เวอร์ชันปลั๊กอินที่ติดตั้งไม่มีรุ่นนี้ คลิกเพื่อเปลี่ยนเวอร์ชัน',
        title: 'รุ่นที่ไม่รองรับ',
        desc: 'เวอร์ชันปลั๊กอินที่ติดตั้งไม่มีรุ่นนี้',
      },
      modelSelectorTooltips: {
        deprecated: 'โมเดลนี้เลิกใช้แล้ว',
      },
      outputVars: {
        files: {
          transfer_method: 'วิธีการโอน ค่าเป็น remote_url หรือ local_file',
          upload_file_id: 'อัปโหลดรหัสไฟล์',
          url: 'URL ของรูปภาพ',
          title: 'ไฟล์ที่สร้างตัวแทน',
          type: 'ประเภทการสนับสนุน ตอนนี้รองรับเฉพาะรูปภาพ',
        },
        text: 'เนื้อหาที่สร้างตัวแทน',
        json: 'ตัวแทนสร้าง JSON',
      },
      checkList: {
        strategyNotSelected: 'ไม่ได้เลือกกลยุทธ์',
      },
      installPlugin: {
        changelog: 'บันทึกการเปลี่ยนแปลง',
        install: 'ติดตั้ง',
        desc: 'เกี่ยวกับการติดตั้งปลั๊กอินต่อไปนี้',
        title: 'ติดตั้งปลั๊กอิน',
        cancel: 'ยกเลิก',
      },
      toolbox: 'เครื่อง มือ',
      maxIterations: 'การทําซ้ําสูงสุด',
      strategyNotFoundDescAndSwitchVersion: 'เวอร์ชันปลั๊กอินที่ติดตั้งไม่มีกลยุทธ์นี้ คลิกเพื่อเปลี่ยนเวอร์ชัน',
      pluginNotInstalledDesc: 'ปลั๊กอินนี้ติดตั้งจาก GitHub โปรดไปที่ปลั๊กอินเพื่อติดตั้งใหม่',
      pluginNotInstalled: 'ไม่ได้ติดตั้งปลั๊กอินนี้',
      toolNotInstallTooltip: '{{tool}} ไม่ได้ติดตั้ง',
      modelNotInstallTooltip: 'ไม่ได้ติดตั้งรุ่นนี้',
      model: 'แบบ',
      strategyNotFoundDesc: 'เวอร์ชันปลั๊กอินที่ติดตั้งไม่มีกลยุทธ์นี้',
      toolNotAuthorizedTooltip: '{{เครื่องมือ}} ไม่ได้รับอนุญาต',
      unsupportedStrategy: 'กลยุทธ์ที่ไม่รองรับ',
      strategyNotSet: 'ไม่ได้ตั้งค่ากลยุทธ์ตัวแทน',
      learnMore: 'ศึกษาเพิ่มเติม',
      pluginNotFoundDesc: 'ปลั๊กอินนี้ติดตั้งจาก GitHub โปรดไปที่ปลั๊กอินเพื่อติดตั้งใหม่',
      notAuthorized: 'ไม่ได้รับอนุญาต',
      configureModel: 'กําหนดค่าแบบจําลอง',
      strategyNotInstallTooltip: '{{strategy}} ไม่ได้ติดตั้ง',
      tools: 'เครื่อง มือ',
      modelNotSelected: 'ไม่ได้เลือกรุ่น',
      linkToPlugin: 'ลิงก์ไปยังปลั๊กอิน',
      parameterSchema: 'แบบจำลองพารามิเตอร์',
      clickToViewParameterSchema: 'คลิกเพื่อดูโครงร่างพารามิเตอร์',
    },
    loop: {
      ErrorMethod: {
        removeAbnormalOutput: 'ลบผลลัพธ์ที่ผิดปกติ',
        operationTerminated: 'ถูกยกเลิก',
        continueOnError: 'ดำเนินการต่อเมื่อมีข้อผิดพลาด',
      },
      breakCondition: 'เงื่อนไขการหยุดลูป',
      output: 'ตัวแปรเอาท์พุท',
      error_one: '{{count}} ข้อผิดพลาด',
      loop_one: '{{count}} ลูป',
      loopMaxCount: 'จำนวนรอบสูงสุด',
      errorResponseMethod: 'วิธีการตอบสนองข้อผิดพลาด',
      loopVariables: 'ตัวแปรลูป',
      deleteDesc: 'การลบโหนดลูปจะลบโหนดลูกทั้งหมด',
      deleteTitle: 'ลบโหนดลูปหรือไม่?',
      error_other: '{{count}} ข้อผิดพลาด',
      loop_other: '{{count}} รอบ',
      loopMaxCountError: 'กรุณาใส่จำนวนรอบสูงสุดที่ถูกต้อง ซึ่งอยู่ระหว่าง 1 ถึง {{maxCount}}',
      comma: ',',
      loopNode: 'น็อดลูป',
      totalLoopCount: 'จำนวนรอบทั้งหมด: {{count}}',
      setLoopVariables: 'กำหนดตัวแปรภายในขอบเขตของลูป',
      input: 'การป้อนข้อมูล',
      finalLoopVariables: 'ตัวแปรในลูปสุดท้าย',
      inputMode: 'โหมดการนำเข้า',
      currentLoop: 'วงจรปัจจุบัน',
      initialLoopVariables: 'ตัวแปรในลูปเริ่มต้น',
      currentLoopCount: 'จำนวนรอบปัจจุบัน: {{count}}',
      variableName: 'ชื่อ ตัวแปร',
      exitConditionTip: 'โหนดลูปต้องมีเงื่อนไขการออกอย่างน้อยหนึ่งเงื่อนไข',
      breakConditionTip: 'แค่ตัวแปรภายในลูปที่มีเงื่อนไขการสิ้นสุดและตัวแปรสำหรับการสนทนาเท่านั้นที่สามารถอ้างอิงได้.',
    },
  },
  tracing: {
    stopBy: 'แวะที่ {{user}}',
  },
  variableReference: {
    conversationVars: 'ตัวแปรการสนทนา',
    noVarsForOperation: 'ไม่มีตัวแปรที่พร้อมใช้งานสําหรับการกําหนดด้วยการดําเนินการที่เลือก',
    noAvailableVars: 'ไม่มีตัวแปรที่ใช้ได้',
    assignedVarsDescription: 'ตัวแปรที่กําหนดต้องเป็นตัวแปรที่เขียนได้ เช่น',
    noAssignedVars: 'ไม่มีตัวแปรที่กําหนด',
  },
  versionHistory: {
    filter: {
      onlyYours: 'เพียงของคุณเท่านั้น',
      empty: 'ไม่พบประวัติการเวอร์ชันที่ตรงกัน',
      onlyShowNamedVersions: 'แสดงเฉพาะรุ่นที่ตั้งชื่อ',
      all: 'ทั้งหมด',
      reset: 'รีเซ็ตตัวกรอง',
    },
    editField: {
      releaseNotes: 'บันทึกการเปิดตัว',
      releaseNotesLengthLimit: 'หมายเหตุการปล่อยไม่สามารถเกิน {{limit}} ตัวอักษร',
      titleLengthLimit: 'ชื่อเรื่องต้องไม่เกิน {{limit}} ตัวอักษร',
      title: 'ชื่อเรื่อง',
    },
    action: {
      updateFailure: 'ไม่สามารถอัปเดตเวอร์ชันได้',
      deleteFailure: 'ลบเวอร์ชันไม่สำเร็จ',
      deleteSuccess: 'เวอร์ชันถูกลบ',
      restoreSuccess: 'เวอร์ชันที่กู้คืน',
      restoreFailure: 'ไม่สามารถกู้คืนเวอร์ชันได้',
      updateSuccess: 'อัปเดตเวอร์ชัน',
      copyIdSuccess: 'คัดลอกรหัสไปยังคลิปบอร์ด',
    },
    releaseNotesPlaceholder: 'อธิบายว่าสิ่งที่เปลี่ยนแปลงไปคืออะไร',
    currentDraft: 'ร่างปัจจุบัน',
    editVersionInfo: 'แก้ไขข้อมูลเวอร์ชัน',
    restorationTip: 'หลังจากการกู้คืนเวอร์ชันแล้ว ร่างปัจจุบันจะถูกเขียนทับ.',
    defaultName: 'เวอร์ชันที่ไม่มีชื่อ',
    deletionTip: 'การลบไม่สามารถย้อนกลับได้ กรุณายืนยัน.',
    nameThisVersion: 'ชื่อเวอร์ชันนี้',
    title: 'เวอร์ชัน',
    latest: 'ล่าสุด',
    copyId: 'คัดลอก ID',
  },
  debug: {
    noData: {
      runThisNode: 'ทำงานโหนดนี้',
      description: 'ผลลัพธ์จากการวิ่งครั้งล่าสุดจะแสดงที่นี่',
    },
    variableInspect: {
      trigger: {
        stop: 'หยุดวิ่ง',
        normal: 'ตรวจสอบตัวแปร',
        cached: 'ดูตัวแปรที่ถูกเก็บไว้ในแคช',
        clear: 'ชัดเจน',
        running: 'สถานะการทำงานของการเก็บข้อมูลชั่วคราว',
      },
      systemNode: 'ระบบ',
      view: 'ดูบันทึก',
      chatNode: 'การสนทนา',
      clearAll: 'รีเซ็ตทั้งหมด',
      envNode: 'สิ่งแวดล้อม',
      emptyLink: 'เรียนรู้เพิ่มเติม',
      edited: 'แก้ไขแล้ว',
      reset: 'รีเซ็ตกลับไปยังค่าครั้งล่าสุด',
      title: 'ตรวจสอบตัวแปร',
      resetConversationVar: 'รีเซ็ตตัวแปรการสนทนาไปยังค่าตั้งต้น',
      emptyTip: 'หลังจากก้าวผ่านโหนดบนผืนผ้าใบหรือเรียกใช้โหนดทีละขั้นตอน คุณสามารถดูค่าปัจจุบันของตัวแปรโหนดใน Variable Inspect ได้',
      clearNode: 'ล้างตัวแปรที่เก็บไว้ในแคช',
    },
    settingsTab: 'การตั้งค่า',
    lastRunTab: 'รอบสุดท้าย',
    relations: {
      dependents: 'ผู้อยู่ในอุปการะ',
      dependencies: 'อ้าง อิง',
      dependenciesDescription: 'โหนดที่โหนดนี้อาศัย',
      noDependencies: 'ไม่มีการพึ่งพา',
      noDependents: 'ไม่มีผู้อยู่ในอุปการะ',
      dependentsDescription: 'โหนดที่อาศัยโหนดนี้',
    },
    relationsTab: 'สัมพันธ์',
    copyLastRun: 'คัดลอกการทำงานล่าสุด',
    noLastRunFound: 'ไม่พบการทำงานก่อนหน้า',
    copyLastRunError: 'ไม่สามารถคัดลอกข้อมูลการทำงานครั้งสุดท้ายได้',
    noMatchingInputsFound: 'ไม่พบข้อมูลที่ตรงกันจากการรันครั้งล่าสุด',
    lastOutput: 'ผลลัพธ์สุดท้าย',
  },
}

export default translation
