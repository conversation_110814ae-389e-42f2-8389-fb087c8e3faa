const translation = {
  createApp: 'UTWÓRZ APLIKACJĘ',
  types: {
    all: 'W<PERSON><PERSON>t<PERSON>',
    chatbot: 'Chatbot',
    agent: 'Agent',
    workflow: 'Przepływ pracy',
    completion: 'Zakończenie',
    advanced: 'Przepływ czatu',
    basic: 'Podstawowy',
  },
  duplicate: 'Duplikuj',
  duplicateTitle: 'Duplikuj aplikację',
  export: 'Eksportuj DSL',
  exportFailed: 'Eksport DSL nie powiódł się.',
  importDSL: 'Importuj plik DSL',
  createFromConfigFile: 'Utwórz z pliku DSL',
  deleteAppConfirmTitle: 'Usunąć tę aplikację?',
  deleteAppConfirmContent:
    'Usunięcie aplikacji jest nieodwracalne. Użytkownicy nie będą mieli już dostępu do twojej aplikacji, a wszystkie konfiguracje monitów i dzienniki zostaną trwale usunięte.',
  appDeleted: 'Aplikacja usunięta',
  appDeleteFailed: 'Nie udało się usunąć aplikacji',
  join: 'Dołącz do społeczności',
  communityIntro:
    'Dyskutuj z członkami zespołu, współtwórcami i deweloperami na różnych kanałach.',
  roadmap: 'Zobacz naszą mapę drogową',
  newApp: {
    startFromBlank: 'Utwórz od podstaw',
    startFromTemplate: 'Utwórz z szablonu',
    workflowWarning: 'Obecnie w fazie beta',
    captionName: 'Ikona i nazwa aplikacji',
    appNamePlaceholder: 'Podaj nazwę swojej aplikacji',
    captionDescription: 'Opis',
    appDescriptionPlaceholder: 'Wprowadź opis aplikacji',
    useTemplate: 'Użyj tego szablonu',
    previewDemo: 'Podgląd demo',
    chatApp: 'Asystent',
    chatAppIntro:
      'Chcę zbudować aplikację opartą na czacie. Ta aplikacja używa formatu pytań i odpowiedzi, umożliwiając wielokrotne rundy ciągłej konwersacji.',
    agentAssistant: 'Nowy asystent agenta',
    completeApp: 'Generator tekstu',
    completeAppIntro:
      'Chcę stworzyć aplikację, która generuje teksty wysokiej jakości na podstawie monitów, takich jak generowanie artykułów, streszczeń, tłumaczeń i innych.',
    showTemplates: 'Chcę wybrać z szablonu',
    hideTemplates: 'Wróć do wyboru trybu',
    Create: 'Utwórz',
    Cancel: 'Anuluj',
    nameNotEmpty: 'Nazwa nie może być pusta',
    appTemplateNotSelected: 'Proszę wybrać szablon',
    appTypeRequired: 'Proszę wybrać typ aplikacji',
    appCreated: 'Aplikacja utworzona',
    appCreateFailed: 'Nie udało się utworzyć aplikacji',
    appCreateDSLErrorPart3: 'Aktualna wersja aplikacji DSL:',
    appCreateDSLErrorPart2: 'Czy chcesz kontynuować?',
    Confirm: 'Potwierdzić',
    caution: 'Ostrożność',
    appCreateDSLWarning: 'Przestroga: Różnica w wersji DSL może mieć wpływ na niektóre funkcje',
    appCreateDSLErrorTitle: 'Niezgodność wersji',
    appCreateDSLErrorPart4: 'Wersja DSL obsługiwana przez system:',
    appCreateDSLErrorPart1: 'Wykryto istotną różnicę w wersjach DSL. Wymuszenie importu może spowodować nieprawidłowe działanie aplikacji.',
    noTemplateFoundTip: 'Spróbuj wyszukać za pomocą różnych słów kluczowych.',
    noAppsFound: 'Nie znaleziono aplikacji',
    foundResults: '{{count}} Wyniki',
    noTemplateFound: 'Nie znaleziono szablonów',
    chatbotUserDescription: 'Szybko zbuduj chatbota opartego na LLM z prostą konfiguracją. Możesz przełączyć się na Chatflow później.',
    optional: 'Fakultatywny',
    workflowUserDescription: 'Twórz autonomiczne przepływy AI wizualnie, z prostotą przeciągnij i upuść.',
    completionUserDescription: 'Szybko zbuduj asystenta AI do zadań generowania tekstu za pomocą prostej konfiguracji.',
    forBeginners: 'Prostsze typy aplikacji',
    agentShortDescription: 'Inteligentny agent z rozumowaniem i autonomicznym wykorzystaniem narzędzi',
    completionShortDescription: 'Asystent AI do zadań generowania tekstu',
    noIdeaTip: 'Nie masz pomysłów? Sprawdź nasze szablony',
    forAdvanced: 'DLA ZAAWANSOWANYCH UŻYTKOWNIKÓW',
    foundResult: '{{count}} Wynik',
    advancedShortDescription: 'Przepływ ulepszony dla wieloturowych czatów',
    learnMore: 'Dowiedz się więcej',
    chatbotShortDescription: 'Chatbot oparty na LLM z prostą konfiguracją',
    chooseAppType: 'Wybierz typ aplikacji',
    agentUserDescription: 'Inteligentny agent zdolny do iteracyjnego wnioskowania i autonomicznego wykorzystania narzędzi do osiągania celów zadań.',
    workflowShortDescription: 'Agentowy przepływ dla inteligentnych automatyzacji',
    advancedUserDescription: 'Przepływ z dodatkowymi funkcjami pamięci i interfejsem chatbota.',
    dropDSLToCreateApp: 'Upuść plik DSL tutaj, aby utworzyć aplikację',
  },
  editApp: 'Edytuj informacje',
  editAppTitle: 'Edytuj informacje o aplikacji',
  editDone: 'Informacje o aplikacji zaktualizowane',
  editFailed: 'Nie udało się zaktualizować informacji o aplikacji',
  iconPicker: {
    ok: 'OK',
    cancel: 'Anuluj',
    emoji: 'Emoji',
    image: 'Obraz',
  },
  switch: 'Przełącz na Orkiestrację Przepływu Pracy',
  switchTipStart:
    'Dla ciebie zostanie utworzona nowa kopia aplikacji, a nowa kopia przełączy się na Orkiestrację Przepływu Pracy. Nowa kopia będzie ',
  switchTip: 'nie pozwoli',
  switchTipEnd: ' na powrót do Podstawowej Orkiestracji.',
  switchLabel: 'Kopia aplikacji do utworzenia',
  removeOriginal: 'Usuń oryginalną aplikację',
  switchStart: 'Rozpocznij przełączanie',
  typeSelector: {
    all: 'WSZYSTKIE Typy',
    chatbot: 'Chatbot',
    agent: 'Agent',
    workflow: 'Przepływ pracy',
    completion: 'Zakończenie',
    advanced: 'Przepływ czatu',
  },
  tracing: {
    title: 'Śledzenie wydajności aplikacji',
    description: 'Konfiguracja zewnętrznego dostawcy LLMOps i śledzenie wydajności aplikacji.',
    config: 'Konfiguruj',
    collapse: 'Zwiń',
    expand: 'Rozwiń',
    tracing: 'Śledzenie',
    disabled: 'Wyłączone',
    disabledTip: 'Najpierw skonfiguruj dostawcę',
    enabled: 'W użyciu',
    tracingDescription: 'Przechwytywanie pełnego kontekstu wykonania aplikacji, w tym wywołań LLM, kontekstu, promptów, żądań HTTP i więcej, do platformy śledzenia stron trzecich.',
    configProviderTitle: {
      configured: 'Skonfigurowano',
      notConfigured: 'Skonfiguruj dostawcę, aby włączyć śledzenie',
      moreProvider: 'Więcej dostawców',
    },
    arize: {
      title: 'Arize',
      description: 'Obserwowalność LLM klasy korporacyjnej, ocena online i offline, monitorowanie i eksperymentowanie — oparta na OpenTelemetry. Zaprojektowana specjalnie dla aplikacji opartych na LLM i agentach.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Otwarta i oparta na OpenTelemetry platforma do obserwowalności, oceny, inżynierii promptów i eksperymentowania dla Twoich przepływów pracy i agentów LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Kompleksowa platforma deweloperska dla każdego etapu cyklu życia aplikacji opartej na LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Śledzenie, oceny, zarządzanie promptami i metryki do debugowania i ulepszania twojej aplikacji LLM.',
    },
    inUse: 'W użyciu',
    configProvider: {
      title: 'Konfiguruj ',
      placeholder: 'Wprowadź swój {{key}}',
      project: 'Projekt',
      publicKey: 'Klucz publiczny',
      secretKey: 'Klucz tajny',
      viewDocsLink: 'Zobacz dokumentację {{key}}',
      removeConfirmTitle: 'Usunąć konfigurację {{key}}?',
      removeConfirmContent: 'Obecna konfiguracja jest w użyciu, jej usunięcie wyłączy funkcję Śledzenia.',
    },
    view: 'Widok',
    opik: {
      description: 'Opik to platforma typu open source do oceny, testowania i monitorowania aplikacji LLM.',
      title: 'Opik',
    },
    weave: {
      title: 'Tkaj',
      description: 'Weave to platforma open-source do oceny, testowania i monitorowania aplikacji LLM.',
    },
    aliyun: {
      title: 'Monitor Chmury',
      description: 'W pełni zarządzana i wolna od konserwacji platforma obserwowalności oferowana przez Alibaba Cloud umożliwia gotowe monitorowanie, śledzenie i oceny aplikacji Dify.',
    },
  },
  answerIcon: {
    description: 'Czy w aplikacji udostępnionej ma być używana ikona aplikacji internetowej do zamiany 🤖.',
    title: 'Użyj ikony web app, aby zastąpić 🤖',
    descriptionInExplore: 'Czy używać ikony aplikacji internetowej do zastępowania 🤖 w Eksploruj',
  },
  importFromDSL: 'Importowanie z DSL',
  importFromDSLUrl: 'Z adresu URL',
  importFromDSLFile: 'Z pliku DSL',
  importFromDSLUrlPlaceholder: 'Wklej tutaj link DSL',
  dslUploader: {
    button: 'Przeciągnij i upuść plik, lub',
    browse: 'Przeglądaj',
  },
  mermaid: {
    handDrawn: 'Ręcznie rysowane',
    classic: 'Klasyczny',
  },
  openInExplore: 'Otwieranie w obszarze Eksploruj',
  newAppFromTemplate: {
    sidebar: {
      Recommended: 'Zalecane',
      Assistant: 'Asystent',
      Writing: 'Pismo',
      Programming: 'Programowanie',
      Workflow: 'Przepływ pracy',
      Agent: 'Agent',
      HR: 'HR',
    },
    searchAllTemplate: 'Przeszukaj wszystkie szablony...',
    byCategories: 'WEDŁUG KATEGORII',
  },
  showMyCreatedAppsOnly: 'Pokaż tylko moje utworzone aplikacje',
  appSelector: {
    params: 'PARAMETRY APLIKACJI',
    noParams: 'Nie są potrzebne żadne parametry',
    placeholder: 'Wybierz aplikację...',
    label: 'Aplikacja',
  },
  structOutput: {
    structured: 'Ustrukturyzowany',
    LLMResponse: 'Odpowiedź LLM',
    notConfiguredTip: 'Strukturalne wyjście nie zostało jeszcze skonfigurowane',
    structuredTip: 'Strukturalne wyniki to funkcja, która zapewnia, że model zawsze generuje odpowiedzi zgodne z dostarczonym schematem JSON.',
    moreFillTip: 'Pokazując maksymalnie 10 poziomów zagnieżdżenia',
    configure: 'Konfiguruj',
    required: 'Wymagane',
    modelNotSupported: 'Model nie jest obsługiwany',
    modelNotSupportedTip: 'Aktualny model nie obsługuje tej funkcji i zostaje automatycznie obniżony do wstrzyknięcia zapytania.',
  },
  accessItemsDescription: {
    anyone: 'Każdy może uzyskać dostęp do aplikacji webowej',
    specific: 'Tylko określone grupy lub członkowie mogą uzyskać dostęp do aplikacji internetowej',
    organization: 'Każdy w organizacji ma dostęp do aplikacji internetowej.',
    external: 'Tylko uwierzytelnieni zewnętrzni użytkownicy mogą uzyskać dostęp do aplikacji internetowej.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Każdy z linkiem',
      specific: 'Specyficzne grupy lub członkowie',
      organization: 'Tylko członkowie w obrębie przedsiębiorstwa',
      external: 'Uwierzytelnieni użytkownicy zewnętrzni',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Szukaj grup i członków',
      allMembers: 'Wszyscy członkowie',
      expand: 'Rozszerz',
      noResult: 'Brak wyniku',
    },
    title: 'Kontrola dostępu do aplikacji internetowej',
    description: 'Ustaw uprawnienia dostępu do aplikacji webowej',
    accessLabel: 'Kto ma dostęp',
    groups_one: '{{count}} GRUPA',
    groups_other: '{{count}} GRUPY',
    members_one: '{{count}} CZŁONEK',
    members_other: '{{count}} CZŁONKÓW',
    noGroupsOrMembers: 'Nie wybrano żadnych grup ani członków',
    webAppSSONotEnabledTip: 'Proszę skontaktować się z administratorem przedsiębiorstwa, aby skonfigurować metodę uwierzytelniania aplikacji internetowej.',
    updateSuccess: 'Aktualizacja powiodła się',
  },
  publishApp: {
    title: 'Kto ma dostęp do aplikacji internetowej',
    notSet: 'Nie ustawiono',
    notSetDesc: 'Obecnie nikt nie może uzyskać dostępu do aplikacji internetowej. Proszę ustawić uprawnienia.',
  },
  accessControl: 'Kontrola dostępu do aplikacji internetowej',
  noAccessPermission: 'Brak uprawnień do dostępu do aplikacji internetowej',
  maxActiveRequests: 'Maksymalne równoczesne żądania',
  maxActiveRequestsPlaceholder: 'Wprowadź 0, aby uzyskać nielimitowane',
  maxActiveRequestsTip: 'Maksymalna liczba jednoczesnych aktywnych żądań na aplikację (0 dla nieograniczonej)',
  gotoAnything: {
    actions: {
      searchPlugins: 'Szukaj wtyczek',
      searchWorkflowNodesHelp: 'Ta funkcja działa tylko podczas wyświetlania przepływu pracy. Najpierw przejdź do przepływu pracy.',
      searchApplicationsDesc: 'Wyszukiwanie aplikacji i przechodzenie do nich',
      searchPluginsDesc: 'Wyszukiwanie i przechodzenie do wtyczek',
      searchApplications: 'Szukaj aplikacji',
      searchKnowledgeBasesDesc: 'Wyszukiwanie i przechodzenie do baz wiedzy',
      searchWorkflowNodesDesc: 'Znajdowanie węzłów w bieżącym przepływie pracy i przechodzenie do nich według nazwy lub typu',
      searchKnowledgeBases: 'Szukaj w bazach wiedzy',
      searchWorkflowNodes: 'Wyszukiwanie węzłów przepływu pracy',
      themeSystem: 'Motyw systemu',
      themeCategoryTitle: 'Temat',
      languageCategoryTitle: 'Język',
      themeDark: 'Ciemny motyw',
      runTitle: 'Polecenia',
      themeLight: 'Jasny motyw',
      themeCategoryDesc: 'Zmień motyw aplikacji',
      languageCategoryDesc: 'Zmień język interfejsu',
      themeDarkDesc: 'Użyj ciemnego wyglądu',
      themeLightDesc: 'Użyj jasnego wyglądu',
      languageChangeDesc: 'Zmień język interfejsu',
      themeSystemDesc: 'Podążaj za wyglądem swojego systemu operacyjnego',
      runDesc: 'Uruchom szybkie polecenia (motyw, język, ...)',
      slashDesc: 'Wykonuj polecenia takie jak /theme, /lang',
    },
    emptyState: {
      noAppsFound: 'Nie znaleziono aplikacji',
      noKnowledgeBasesFound: 'Nie znaleziono baz wiedzy',
      noWorkflowNodesFound: 'Nie znaleziono węzłów przepływu pracy',
      noPluginsFound: 'Nie znaleziono wtyczek',
      tryDifferentTerm: 'Spróbuj innego terminu wyszukiwania lub usuń filtr {{mode}}',
      trySpecificSearch: 'Spróbuj {{shortcuts}} dla konkretnych wyszukiwań',
    },
    groups: {
      apps: 'Aplikacje',
      workflowNodes: 'Węzły przepływu pracy',
      knowledgeBases: 'Bazy wiedzy',
      plugins: 'Wtyczki',
      commands: 'Polecenia',
    },
    useAtForSpecific: 'Użyj @ dla określonych typów',
    searchPlaceholder: 'Wyszukaj lub wpisz @ dla poleceń...',
    searching: 'Wyszukiwanie...',
    noResults: 'Nie znaleziono wyników',
    searchTitle: 'Szukaj czegokolwiek',
    someServicesUnavailable: 'Niektóre usługi wyszukiwania są niedostępne',
    clearToSearchAll: 'Wyczyść @, aby przeszukać wszystko',
    searchTemporarilyUnavailable: 'Wyszukiwanie chwilowo niedostępne',
    servicesUnavailableMessage: 'W przypadku niektórych usług wyszukiwania mogą występować problemy. Spróbuj ponownie za chwilę.',
    searchFailed: 'Wyszukiwanie nie powiodło się',
    searchHint: 'Zacznij pisać, aby natychmiast wszystko przeszukać',
    commandHint: 'Wpisz @, aby przeglądać według kategorii',
    selectSearchType: 'Wybierz, czego chcesz szukać',
    resultCount: '{{count}} wynik',
    resultCount_other: '{{count}} wyników',
    inScope: 'w {{scope}}s',
    noMatchingCommands: 'Nie znaleziono pasujących poleceń',
    tryDifferentSearch: 'Spróbuj użyć innego hasła',
  },
}

export default translation
