const translation = {
  common: {
    undo: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    redo: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    editing: 'Bearbeitung',
    autoSaved: 'Automatisch gespeichert',
    unpublished: 'Unveröffentlicht',
    published: 'Veröffentlicht',
    publish: 'Veröffentlichen',
    update: 'Aktualisieren',
    run: 'Ausführen',
    running: 'Wird ausgeführt',
    inRunMode: 'Im Ausführungsmodus',
    inPreview: 'In der Vorschau',
    inPreviewMode: 'Im Vorschaumodus',
    preview: 'Vorschau',
    viewRunHistory: 'Ausführungsverlauf anzeigen',
    runHistory: 'Ausführungsverlauf',
    goBackToEdit: 'Zurück zum Editor',
    conversationLog: 'Konversationsprotokoll',
    features: 'Funktionen',
    debugAndPreview: 'Vorschau',
    restart: 'Neustarten',
    currentDraft: 'Aktueller Entwurf',
    currentDraftUnpublished: 'Aktueller Entwurf unveröffentlicht',
    latestPublished: 'Zuletzt veröffentlicht',
    publishedAt: 'Veröffentlicht am',
    restore: 'Wiederherstellen',
    runApp: 'App ausführen',
    batchRunApp: 'App im Batch-Modus ausführen',
    accessAPIReference: 'API-Referenz aufrufen',
    embedIntoSite: 'In die Webseite einbetten',
    addTitle: 'Titel hinzufügen...',
    addDescription: 'Beschreibung hinzufügen...',
    noVar: 'Keine Variable',
    searchVar: 'Variable suchen',
    variableNamePlaceholder: 'Variablenname',
    setVarValuePlaceholder: 'Variable setzen',
    needConnectTip: 'Dieser Schritt ist mit nichts verbunden',
    maxTreeDepth: 'Maximales Limit von {{depth}} Knoten pro Ast',
    workflowProcess: 'Arbeitsablauf',
    notRunning: 'Noch nicht ausgeführt',
    previewPlaceholder: 'Geben Sie den Inhalt in das Feld unten ein, um das Debuggen des Chatbots zu starten',
    effectVarConfirm: {
      title: 'Variable entfernen',
      content: 'Die Variable wird in anderen Knoten verwendet. Möchten Sie sie trotzdem entfernen?',
    },
    insertVarTip: 'Drücken Sie die Taste \'/\' zum schnellen Einfügen',
    processData: 'Daten verarbeiten',
    input: 'Eingabe',
    output: 'Ausgabe',
    jinjaEditorPlaceholder: 'Tippen Sie \'/\' oder \'{\' um eine Variable einzufügen',
    viewOnly: 'Nur anzeigen',
    showRunHistory: 'Ausführungsverlauf anzeigen',
    enableJinja: 'Jinja-Vorlagenunterstützung aktivieren',
    learnMore: 'Mehr erfahren',
    copy: 'Kopieren',
    duplicate: 'Duplizieren',
    pasteHere: 'Hier einfügen',
    pointerMode: 'Zeigermodus',
    handMode: 'Handmodus',
    model: 'Modell',
    workflowAsTool: 'Workflow als Tool',
    configureRequired: 'Konfiguration erforderlich',
    configure: 'Konfigurieren',
    manageInTools: 'In den Tools verwalten',
    workflowAsToolTip: 'Nach dem Workflow-Update ist eine Neukonfiguration des Tools erforderlich.',
    viewDetailInTracingPanel: 'Details anzeigen',
    importDSL: 'DSL importieren',
    importFailure: 'Fehler beim Import',
    syncingData: 'Synchronisieren von Daten, nur wenige Sekunden.',
    chooseDSL: 'Wählen Sie eine DSL(yml)-Datei',
    importSuccess: 'Erfolg beim Import',
    importDSLTip: 'Der aktuelle Entwurf wird überschrieben. Exportieren Sie den Workflow vor dem Import als Backup.',
    overwriteAndImport: 'Überschreiben und Importieren',
    backupCurrentDraft: 'Aktuellen Entwurf sichern',
    parallelTip: {
      click: {
        title: 'Klicken',
        desc: 'hinzuzufügen',
      },
      drag: {
        title: 'Ziehen',
        desc: 'um eine Verbindung herzustellen',
      },
      limit: 'Die Parallelität ist auf {{num}} Zweige beschränkt.',
      depthLimit: 'Begrenzung der parallelen Verschachtelungsschicht von {{num}} Schichten',
    },
    parallelRun: 'Paralleler Lauf',
    disconnect: 'Trennen',
    jumpToNode: 'Zu diesem Knoten springen',
    addParallelNode: 'Parallelen Knoten hinzufügen',
    parallel: 'PARALLEL',
    branch: 'ZWEIG',
    featuresDocLink: 'Weitere Informationen',
    ImageUploadLegacyTip: 'Sie können jetzt Dateitypvariablen im Startformular erstellen. Wir werden die Funktion zum Hochladen von Bildern in Zukunft nicht mehr unterstützen.',
    fileUploadTip: 'Die Funktionen zum Hochladen von Bildern wurden auf das Hochladen von Dateien aktualisiert.',
    featuresDescription: 'Verbessern Sie die Benutzererfahrung von Web-Apps',
    importWarning: 'Vorsicht',
    importWarningDetails: 'Der Unterschied zwischen den DSL-Versionen kann sich auf bestimmte Funktionen auswirken',
    openInExplore: 'In Explore öffnen',
    onFailure: 'Bei Ausfall',
    addFailureBranch: 'Fail-Branch hinzufügen',
    loadMore: 'Weitere Workflows laden',
    noHistory: 'Keine Geschichte',
    exportSVG: 'Als SVG exportieren',
    versionHistory: 'Versionsverlauf',
    publishUpdate: 'Update veröffentlichen',
    exportImage: 'Bild exportieren',
    exportJPEG: 'Als JPEG exportieren',
    exitVersions: 'Ausgangsversionen',
    exportPNG: 'Als PNG exportieren',
    addBlock: 'Knoten hinzufügen',
    needEndNode: 'Der Endknoten muss hinzugefügt werden.',
    needAnswerNode: 'Der Antwortknoten muss hinzugefügt werden.',
    tagBound: 'Anzahl der Apps, die dieses Tag verwenden',
    currentWorkflow: 'Aktueller Arbeitsablauf',
    currentView: 'Aktuelle Ansicht',
  },
  env: {
    envPanelTitle: 'Umgebungsvariablen',
    envDescription: 'Umgebungsvariablen können zur Speicherung privater Informationen und Anmeldedaten verwendet werden. Sie sind schreibgeschützt und können beim Export vom DSL-File getrennt werden.',
    envPanelButton: 'Variable hinzufügen',
    modal: {
      title: 'Umgebungsvariable hinzufügen',
      editTitle: 'Umgebungsvariable bearbeiten',
      type: 'Typ',
      name: 'Name',
      namePlaceholder: 'Umgebungsname',
      value: 'Wert',
      valuePlaceholder: 'Umgebungswert',
      secretTip: 'Wird verwendet, um sensible Informationen oder Daten zu definieren, wobei DSL-Einstellungen zur Verhinderung von Lecks konfiguriert sind.',
      description: 'Beschreibung',
      descriptionPlaceholder: 'Beschreiben Sie die Variable',
    },
    export: {
      title: 'Geheime Umgebungsvariablen exportieren?',
      checkbox: 'Geheime Werte exportieren',
      ignore: 'DSL exportieren',
      export: 'DSL mit geheimen Werten exportieren',
    },
  },
  chatVariable: {
    panelTitle: 'Gesprächsvariablen',
    panelDescription: 'Gesprächsvariablen werden verwendet, um interaktive Informationen zu speichern, die das LLM benötigt, einschließlich Gesprächsverlauf, hochgeladene Dateien und Benutzereinstellungen. Sie sind les- und schreibbar.',
    docLink: 'Besuchen Sie unsere Dokumentation für weitere Informationen.',
    button: 'Variable hinzufügen',
    modal: {
      title: 'Gesprächsvariable hinzufügen',
      editTitle: 'Gesprächsvariable bearbeiten',
      name: 'Name',
      namePlaceholder: 'Variablenname',
      type: 'Typ',
      value: 'Standardwert',
      valuePlaceholder: 'Standardwert, leer lassen für keine Festlegung',
      description: 'Beschreibung',
      descriptionPlaceholder: 'Beschreiben Sie die Variable',
      editInJSON: 'In JSON bearbeiten',
      oneByOne: 'Einzeln hinzufügen',
      editInForm: 'Im Formular bearbeiten',
      arrayValue: 'Wert',
      addArrayValue: 'Wert hinzufügen',
      objectKey: 'Schlüssel',
      objectType: 'Typ',
      objectValue: 'Standardwert',
    },
    storedContent: 'Gespeicherter Inhalt',
    updatedAt: 'Aktualisiert am ',
  },
  changeHistory: {
    title: 'Änderungsverlauf',
    placeholder: 'Du hast noch nichts geändert',
    clearHistory: 'Änderungsverlauf löschen',
    hint: 'Hinweis',
    hintText: 'Änderungen werden im Änderungsverlauf aufgezeichnet, der für die Dauer dieser Sitzung auf Ihrem Gerät gespeichert wird. Dieser Verlauf wird gelöscht, wenn Sie den Editor verlassen.',
    stepBackward_one: '{{count}} Schritt zurück',
    stepBackward_other: '{{count}} Schritte zurück',
    stepForward_one: '{{count}} Schritt vorwärts',
    stepForward_other: '{{count}} Schritte vorwärts',
    sessionStart: 'Sitzungsstart',
    currentState: 'Aktueller Zustand',
    noteAdd: 'Notiz hinzugefügt',
    noteChange: 'Notiz geändert',
    noteDelete: 'Notiz gelöscht',
    edgeDelete: 'Knoten getrennt',
    nodeAdd: 'Knoten hinzugefügt',
    nodeTitleChange: 'Knotenüberschrift geändert',
    nodePaste: 'Knoten eingefügt',
    nodeResize: 'Knoten verkleinert',
    nodeDescriptionChange: 'Die Knotenbeschreibung wurde geändert',
    nodeChange: 'Knoten geändert',
    nodeConnect: 'Node verbunden',
    nodeDragStop: 'Knoten verschoben',
    nodeDelete: 'Knoten gelöscht',
  },
  errorMsg: {
    fieldRequired: '{{field}} ist erforderlich',
    authRequired: 'Autorisierung ist erforderlich',
    invalidJson: '{{field}} ist ein ungültiges JSON',
    fields: {
      variable: 'Variablenname',
      variableValue: 'Variablenwert',
      code: 'Code',
      model: 'Modell',
      rerankModel: 'Neusortierungsmodell',
      visionVariable: 'Vision variabel',
    },
    invalidVariable: 'Ungültige Variable',
    rerankModelRequired: 'Bevor Sie das Rerank-Modell aktivieren, bestätigen Sie bitte, dass das Modell in den Einstellungen erfolgreich konfiguriert wurde.',
    toolParameterRequired: '{{field}}: Parameter [{{param}}] ist erforderlich',
    noValidTool: '{{field}} kein gültiges Werkzeug ausgewählt',
  },
  singleRun: {
    testRun: 'Testlauf ',
    startRun: 'Lauf starten',
    running: 'Wird ausgeführt',
    testRunIteration: 'Testlaufiteration',
    back: 'Zurück',
    iteration: 'Iteration',
    loop: 'Schleife',
  },
  tabs: {
    'tools': 'Werkzeuge',
    'allTool': 'Alle',
    'customTool': 'Benutzerdefiniert',
    'workflowTool': 'Arbeitsablauf',
    'question-understand': 'Fragen verstehen',
    'logic': 'Logik',
    'transform': 'Transformieren',
    'utilities': 'Dienstprogramme',
    'noResult': 'Kein Ergebnis gefunden',
    'searchTool': 'Suchwerkzeug',
    'plugin': 'Stecker',
    'agent': 'Agenten-Strategie',
    'searchBlock': 'Suchknoten',
    'blocks': 'Knoten',
    'allAdded': 'Alle hinzugefügt',
    'addAll': 'Alles hinzufügen',
  },
  blocks: {
    'start': 'Start',
    'end': 'Ende',
    'answer': 'Antwort',
    'llm': 'LLM',
    'knowledge-retrieval': 'Wissensabruf',
    'question-classifier': 'Fragenklassifizierer',
    'if-else': 'WENN/SONST',
    'code': 'Code',
    'template-transform': 'Vorlage',
    'http-request': 'HTTP-Anfrage',
    'variable-assigner': 'Variablen-Zuweiser',
    'variable-aggregator': 'Variablen-Aggregator',
    'assigner': 'Variablenzuweiser',
    'iteration-start': 'Iterationsstart',
    'iteration': 'Iteration',
    'parameter-extractor': 'Parameter-Extraktor',
    'list-operator': 'List-Operator',
    'document-extractor': 'Doc Extraktor',
    'agent': 'Agent',
    'loop': 'Schleife',
    'loop-start': 'Schleifenbeginn',
    'loop-end': 'Schleife beenden',
  },
  blocksAbout: {
    'start': 'Definieren Sie die Anfangsparameter zum Starten eines Workflows',
    'end': 'Definieren Sie das Ende und den Ergebnistyp eines Workflows',
    'answer': 'Definieren Sie den Antwortinhalt einer Chat-Konversation',
    'llm': 'Große Sprachmodelle aufrufen, um Fragen zu beantworten oder natürliche Sprache zu verarbeiten',
    'knowledge-retrieval': 'Ermöglicht das Abfragen von Textinhalten, die sich auf Benutzerfragen aus der Wissensdatenbank beziehen',
    'question-classifier': 'Definieren Sie die Klassifizierungsbedingungen von Benutzerfragen, LLM kann basierend auf der Klassifikationsbeschreibung festlegen, wie die Konversation fortschreitet',
    'if-else': 'Ermöglicht das Aufteilen des Workflows in zwei Zweige basierend auf if/else-Bedingungen',
    'code': 'Ein Stück Python- oder NodeJS-Code ausführen, um benutzerdefinierte Logik zu implementieren',
    'template-transform': 'Daten in Zeichenfolgen mit Jinja-Vorlagensyntax umwandeln',
    'http-request': 'Ermöglichen, dass Serveranforderungen über das HTTP-Protokoll gesendet werden',
    'variable-assigner': 'Variablen aus mehreren Zweigen in eine einzige Variable zusammenführen, um eine einheitliche Konfiguration der nachgelagerten Knoten zu ermöglichen.',
    'assigner': 'Der Variablenzuweisungsknoten wird verwendet, um beschreibbaren Variablen (wie Gesprächsvariablen) Werte zuzuweisen.',
    'variable-aggregator': 'Variablen aus mehreren Zweigen in eine einzige Variable zusammenführen, um eine einheitliche Konfiguration der nachgelagerten Knoten zu ermöglichen.',
    'iteration': 'Mehrere Schritte an einem Listenobjekt ausführen, bis alle Ergebnisse ausgegeben wurden.',
    'parameter-extractor': 'Verwenden Sie LLM, um strukturierte Parameter aus natürlicher Sprache für Werkzeugaufrufe oder HTTP-Anfragen zu extrahieren.',
    'list-operator': 'Wird verwendet, um Array-Inhalte zu filtern oder zu sortieren.',
    'document-extractor': 'Wird verwendet, um hochgeladene Dokumente in Textinhalte zu analysieren, die für LLM leicht verständlich sind.',
    'agent': 'Aufruf großer Sprachmodelle zur Beantwortung von Fragen oder zur Verarbeitung natürlicher Sprache',
    'loop': 'Führen Sie eine Schleife aus, bis die Abschlussbedingungen erfüllt sind oder die maximalen Schleifenanzahl erreicht ist.',
    'loop-end': 'Entspricht "break". Dieser Knoten hat keine Konfigurationselemente. Wenn der Schleifenrumpf diesen Knoten erreicht, wird die Schleife beendet.',
  },
  operator: {
    zoomIn: 'Vergrößern',
    zoomOut: 'Verkleinern',
    zoomTo50: 'Auf 50% vergrößern',
    zoomTo100: 'Auf 100% vergrößern',
    zoomToFit: 'An Bildschirm anpassen',
    selectionAlignment: 'Ausrichtung der Auswahl',
    alignLeft: 'Links',
    alignTop: 'Nach oben',
    distributeVertical: 'Vertikal verteilen',
    alignBottom: 'Nach unten',
    distributeHorizontal: 'Horizontal verteilen',
    vertical: 'Vertikal',
    alignMiddle: 'Mitte',
    alignCenter: 'Mitte',
    alignRight: 'Rechts',
    alignNodes: 'Knoten ausrichten',
    horizontal: 'Horizontal',
  },
  panel: {
    userInputField: 'Benutzereingabefeld',
    helpLink: 'Hilfelink',
    about: 'Über',
    createdBy: 'Erstellt von ',
    nextStep: 'Nächster Schritt',
    runThisStep: 'Diesen Schritt ausführen',
    checklist: 'Checkliste',
    checklistTip: 'Stellen Sie sicher, dass alle Probleme vor der Veröffentlichung gelöst sind',
    checklistResolved: 'Alle Probleme wurden gelöst',
    change: 'Ändern',
    optional: '(optional)',
    moveToThisNode: 'Bewege zu diesem Knoten',
    selectNextStep: 'Nächsten Schritt auswählen',
    addNextStep: 'Fügen Sie den nächsten Schritt in diesem Arbeitsablauf hinzu.',
    organizeBlocks: 'Knoten organisieren',
    changeBlock: 'Knoten ändern',
    maximize: 'Maximiere die Leinwand',
    minimize: 'Vollbildmodus beenden',
  },
  nodes: {
    common: {
      outputVars: 'Ausgabevariablen',
      insertVarTip: 'Variable einfügen',
      memory: {
        memory: 'Speicher',
        memoryTip: 'Einstellungen des Chat-Speichers',
        windowSize: 'Fenstergröße',
        conversationRoleName: 'Rollenname in der Konversation',
        user: 'Benutzer-Präfix',
        assistant: 'Assistenten-Präfix',
      },
      memories: {
        title: 'Erinnerungen',
        tip: 'Chat-Speicher',
        builtIn: 'Eingebaut',
      },
      errorHandle: {
        none: {
          title: 'Nichts',
          desc: 'Der Knoten wird nicht mehr ausgeführt, wenn eine Ausnahme auftritt und nicht behandelt wird',
        },
        defaultValue: {
          title: 'Standardwert',
          desc: 'Wenn ein Fehler auftritt, geben Sie einen statischen Ausgabeinhalt an.',
          tip: 'Bei einem Fehler wird der untere Wert zurückgegeben.',
          inLog: 'Knotenausnahme, Ausgabe nach Vorschlagswerten.',
          output: 'Standardwert für die Ausgabe',
        },
        failBranch: {
          title: 'Fehlgeschlagener Zweig',
          desc: 'Wenn ein Fehler auftritt, wird der Ausnahmezweig ausgeführt',
          customize: 'Wechseln Sie zur Arbeitsfläche, um die Fehlerverzweigungslogik anzupassen.',
          customizeTip: 'Wenn der Fail-Zweig aktiviert ist, wird der Prozess durch Ausnahmen, die von Knoten ausgelöst werden, nicht beendet. Stattdessen wird automatisch der vordefinierte Fehlerzweig ausgeführt, sodass Sie flexibel Fehlermeldungen, Berichte, Korrekturen oder Überspringen von Aktionen bereitstellen können.',
          inLog: 'Knotenausnahme, führt den Fail-Zweig automatisch aus. Die Knotenausgabe gibt einen Fehlertyp und eine Fehlermeldung zurück und übergibt sie an den Downstream.',
        },
        partialSucceeded: {
          tip: 'Es gibt {{num}} Knoten im Prozess, die nicht normal laufen, bitte gehen Sie zur Ablaufverfolgung, um die Protokolle zu überprüfen.',
        },
        title: 'Fehlerbehandlung',
        tip: 'Ausnahmebehandlungsstrategie, die ausgelöst wird, wenn ein Knoten auf eine Ausnahme stößt.',
      },
      retry: {
        retry: 'Wiederholen',
        retryOnFailure: 'Wiederholen bei Fehler',
        maxRetries: 'Max. Wiederholungen',
        retryInterval: 'Wiederholungsintervall',
        retryTimes: 'Wiederholen Sie {{times}} mal bei einem Fehler',
        retrying: 'Wiederholung...',
        retrySuccessful: 'Wiederholen erfolgreich',
        retryFailed: 'Wiederholung fehlgeschlagen',
        retryFailedTimes: '{{times}} fehlgeschlagene Wiederholungen',
        times: 'mal',
        ms: 'Frau',
        retries: '{{num}} Wiederholungen',
      },
      typeSwitch: {
        input: 'Eingabewert',
        variable: 'Verwende die Variable',
      },
    },
    start: {
      required: 'erforderlich',
      inputField: 'Eingabefeld',
      builtInVar: 'Eingebaute Variablen',
      outputVars: {
        query: 'Benutzereingabe',
        memories: {
          des: 'Konversationsverlauf',
          type: 'Nachrichtentyp',
          content: 'Nachrichteninhalt',
        },
        files: 'Dateiliste',
      },
      noVarTip: 'Legen Sie Eingaben fest, die im Workflow verwendet werden können',
    },
    end: {
      outputs: 'Ausgaben',
      output: {
        type: 'Ausgabetyp',
        variable: 'Ausgabevariable',
      },
      type: {
        'none': 'Keine',
        'plain-text': 'Klartext',
        'structured': 'Strukturiert',
      },
    },
    answer: {
      answer: 'Antwort',
      outputVars: 'Ausgabevariablen',
    },
    llm: {
      model: 'Modell',
      variables: 'Variablen',
      context: 'Kontext',
      contextTooltip: 'Sie können Wissen als Kontext importieren',
      notSetContextInPromptTip: 'Um die Kontextfunktion zu aktivieren, füllen Sie die Kontextvariable im PROMPT aus.',
      prompt: 'Prompt',
      roleDescription: {
        system: 'Geben Sie hochrangige Anweisungen für die Konversation',
        user: 'Geben Sie dem Modell Anweisungen, Abfragen oder beliebigen texteingabebasierten Input',
        assistant: 'Die Antworten des Modells basierend auf den Benutzernachrichten',
      },
      addMessage: 'Nachricht hinzufügen',
      vision: 'Vision',
      files: 'Dateien',
      resolution: {
        name: 'Auflösung',
        high: 'Hoch',
        low: 'Niedrig',
      },
      outputVars: {
        output: 'Generierter Inhalt',
        usage: 'Nutzungsinformationen des Modells',
      },
      singleRun: {
        variable: 'Variable',
      },
      sysQueryInUser: 'sys.query in Benutzernachricht erforderlich',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Bitte beenden Sie die Bearbeitung des aktuellen Feldes, bevor Sie das Schema speichern.',
        },
        stringValidations: 'Stringvalidierungen',
        addField: 'Feld hinzufügen',
        generateJsonSchema: 'JSON-Schema generieren',
        back: 'Zurück',
        addChildField: 'Kindfeld hinzufügen',
        generationTip: 'Sie können natürliche Sprache verwenden, um schnell ein JSON-Schema zu erstellen.',
        title: 'Strukturiertes Ausgabeschema',
        resetDefaults: 'Zurücksetzen',
        showAdvancedOptions: 'Erweiterte Optionen anzeigen',
        fieldNamePlaceholder: 'Feldname',
        descriptionPlaceholder: 'Fügen Sie eine Beschreibung hinzu.',
        resultTip: 'Hier ist das generierte Ergebnis. Wenn Sie nicht zufrieden sind, können Sie zurückgehen und Ihre Eingabeaufforderung ändern.',
        generatedResult: 'Generiertes Ergebnis',
        promptTooltip: 'Konvertiere die Textbeschreibung in eine standardisierte JSON-Schema-Struktur.',
        promptPlaceholder: 'Beschreibe dein JSON-Schema...',
        doc: 'Erfahren Sie mehr über strukturierten Output.',
        required: 'erforderlich',
        generate: 'Generieren',
        apply: 'Bewerben',
        import: 'Import aus JSON',
        generating: 'Generiere JSON-Schema...',
        instruction: 'Anleitung',
        regenerate: 'Regenerieren',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Abfragevariable',
      knowledge: 'Wissen',
      outputVars: {
        output: 'Abgerufene segmentierte Daten',
        content: 'Segmentierter Inhalt',
        title: 'Segmentierter Titel',
        icon: 'Segmentiertes Symbol',
        url: 'Segmentierte URL',
        metadata: 'Weitere Metadaten',
      },
      metadata: {
        options: {
          disabled: {
            title: 'Deaktiviert',
            subTitle: 'Keine Aktivierung der Metadatfilterung',
          },
          automatic: {
            desc: 'Automatisch Filterbedingungen für Metadaten basierend auf Abfragevariablen generieren.',
            title: 'Automatisch',
            subTitle: 'Automatisch Metadatenfilterbedingungen basierend auf der Benutzeranfrage generieren',
          },
          manual: {
            title: 'Handbuch',
            subTitle: 'Manuell Filterbedingungen für Metadaten hinzufügen',
          },
        },
        panel: {
          placeholder: 'Wert eingeben',
          datePlaceholder: 'Wählen Sie eine Zeit...',
          add: 'Bedingung hinzufügen',
          title: 'Metadatenfilterbedingungen',
          select: 'Wählen Sie eine Variable aus...',
          conditions: 'Bedingungen',
          search: 'Suchmetadaten',
        },
        title: 'Metadatenfilterung',
        tip: 'Metadatenfilterung ist der Prozess, Metadatenattribute (wie Tags, Kategorien oder Zugriffsberechtigungen) zu verwenden, um die Abfrage und Kontrolle der relevanten Informationen innerhalb eines Systems zu verfeinern.',
      },
    },
    http: {
      inputVars: 'Eingabevariablen',
      api: 'API',
      apiPlaceholder: 'Geben Sie die URL ein, tippen Sie ‘/’, um Variable einzufügen',
      notStartWithHttp: 'API sollte mit http:// oder https:// beginnen',
      key: 'Schlüssel',
      value: 'Wert',
      bulkEdit: 'Massenerfassung',
      keyValueEdit: 'Schlüssel-Wert-Erfassung',
      headers: 'Header',
      params: 'Parameter',
      body: 'Body',
      outputVars: {
        body: 'Antwortinhalt',
        statusCode: 'Antwortstatuscode',
        headers: 'Antwort-Header-Liste im JSON-Format',
        files: 'Dateiliste',
      },
      authorization: {
        'authorization': 'Autorisierung',
        'authorizationType': 'Autorisierungstyp',
        'no-auth': 'Keine',
        'api-key': 'API-Schlüssel',
        'auth-type': 'Autorisierungstyp',
        'basic': 'Basis',
        'bearer': 'Bearer',
        'custom': 'Benutzerdefiniert',
        'api-key-title': 'API-Schlüssel',
        'header': 'Header',
      },
      insertVarPlaceholder: 'tippen Sie ‘/’, um Variable einzufügen',
      timeout: {
        title: 'Zeitüberschreitung',
        connectLabel: 'Verbindungs-Zeitüberschreitung',
        connectPlaceholder: 'Geben Sie die Verbindungs-Zeitüberschreitung in Sekunden ein',
        readLabel: 'Lese-Zeitüberschreitung',
        readPlaceholder: 'Geben Sie die Lese-Zeitüberschreitung in Sekunden ein',
        writeLabel: 'Schreib-Zeitüberschreitung',
        writePlaceholder: 'Geben Sie die Schreib-Zeitüberschreitung in Sekunden ein',
      },
      type: 'Art',
      binaryFileVariable: 'Variable der Binärdatei',
      extractListPlaceholder: 'Geben Sie den Index des Listeneintrags ein, geben Sie \'/\' ein, fügen Sie die Variable ein',
      curl: {
        title: 'Importieren von cURL',
        placeholder: 'Fügen Sie hier die cURL-Zeichenfolge ein',
      },
      verifySSL: {
        title: 'SSL-Zertifikat überprüfen',
        warningTooltip: 'Das Deaktivieren der SSL-Überprüfung wird für Produktionsumgebungen nicht empfohlen. Dies sollte nur in der Entwicklung oder im Test verwendet werden, da es die Verbindung anfällig für Sicherheitsbedrohungen wie Man-in-the-Middle-Angriffe macht.',
      },
    },
    code: {
      inputVars: 'Eingabevariablen',
      outputVars: 'Ausgabevariablen',
      advancedDependencies: 'Erweiterte Abhängigkeiten',
      advancedDependenciesTip: 'Fügen Sie hier einige vorinstallierte Abhängigkeiten hinzu, die mehr Zeit in Anspruch nehmen oder nicht standardmäßig eingebaut sind',
      searchDependencies: 'Abhängigkeiten suchen',
      syncFunctionSignature: 'Synchronisiere die Funktionssignatur mit dem Code',
    },
    templateTransform: {
      inputVars: 'Eingabevariablen',
      code: 'Code',
      codeSupportTip: 'Unterstützt nur Jinja2',
      outputVars: {
        output: 'Transformierter Inhalt',
      },
    },
    ifElse: {
      if: 'Wenn',
      else: 'Sonst',
      elseDescription: 'Wird verwendet, um die Logik zu definieren, die ausgeführt werden soll, wenn die if-Bedingung nicht erfüllt ist.',
      and: 'und',
      or: 'oder',
      operator: 'Operator',
      notSetVariable: 'Bitte setzen Sie zuerst die Variable',
      comparisonOperator: {
        'contains': 'enthält',
        'not contains': 'enthält nicht',
        'start with': 'beginnt mit',
        'end with': 'endet mit',
        'is': 'ist',
        'is not': 'ist nicht',
        'empty': 'ist leer',
        'not empty': 'ist nicht leer',
        'null': 'ist null',
        'not null': 'ist nicht null',
        'not exists': 'existiert nicht',
        'in': 'in',
        'all of': 'alle',
        'exists': 'existiert',
        'not in': 'nicht in',
        'after': 'nach',
        'before': 'vor',
      },
      enterValue: 'Wert eingeben',
      addCondition: 'Bedingung hinzufügen',
      conditionNotSetup: 'Bedingung NICHT eingerichtet',
      selectVariable: 'Variable auswählen...',
      optionName: {
        video: 'Video',
        url: 'URL (Englisch)',
        image: 'Bild',
        localUpload: 'Lokaler Upload',
        audio: 'Audio',
        doc: 'Doktor',
      },
      select: 'Auswählen',
      addSubVariable: 'Untervariable',
    },
    variableAssigner: {
      title: 'Variablen zuweisen',
      outputType: 'Ausgabetyp',
      varNotSet: 'Variable nicht gesetzt',
      noVarTip: 'Fügen Sie die zuzuweisenden Variablen hinzu',
      type: {
        string: 'String',
        number: 'Nummer',
        object: 'Objekt',
        array: 'Array',
      },
      aggregationGroup: 'Aggregationsgruppe',
      aggregationGroupTip: 'Durch Aktivieren dieser Funktion kann der Variablen-Aggregator mehrere Variablensätze aggregieren.',
      addGroup: 'Gruppe hinzufügen',
      outputVars: {
        varDescribe: 'Ausgabe {{groupName}}',
      },
      setAssignVariable: 'Zuweisungsvariable festlegen',
    },
    assigner: {
      'assignedVariable': 'Zugewiesene Variable',
      'writeMode': 'Schreibmodus',
      'writeModeTip': 'Wenn die ZUGEWIESENE VARIABLE ein Array ist, fügt der Anhängemodus am Ende hinzu.',
      'over-write': 'Überschreiben',
      'append': 'Anhängen',
      'plus': 'Plus',
      'clear': 'Löschen',
      'setVariable': 'Variable setzen',
      'variable': 'Variable',
      'operations': {
        'title': 'Operation',
        'clear': 'Klar',
        'over-write': 'Überschreiben',
        'set': 'Garnitur',
        '-=': '-=',
        '+=': '+=',
        '/=': '/=',
        'append': 'Anfügen',
        'extend': 'Ausdehnen',
        '*=': '*=',
        'overwrite': 'Überschreiben',
        'remove-first': 'Erste entfernen',
        'remove-last': 'Letzte entfernen',
      },
      'setParameter': 'Parameter setzen...',
      'noVarTip': 'Klicken Sie auf die Schaltfläche "+", um Variablen hinzuzufügen',
      'variables': 'Variablen',
      'noAssignedVars': 'Keine verfügbaren zugewiesenen Variablen',
      'selectAssignedVariable': 'Zugewiesene Variable auswählen...',
      'varNotSet': 'Variable NICHT gesetzt',
      'assignedVarsDescription': 'Zugewiesene Variablen müssen beschreibbare Variablen sein, z. B. Konversationsvariablen.',
    },
    tool: {
      inputVars: 'Eingabevariablen',
      outputVars: {
        text: 'durch das Tool generierter Inhalt',
        files: {
          title: 'durch das Tool generierte Dateien',
          type: 'Unterstützungstyp. Derzeit nur Bild unterstützt',
          transfer_method: 'Übertragungsmethode. Der Wert ist remote_url oder local_file',
          url: 'Bild-URL',
          upload_file_id: 'Hochgeladene Datei-ID',
        },
        json: 'von einem Tool generiertes JSON',
      },
      authorize: 'Autorisieren',
      insertPlaceholder2: 'Fügen Sie die Variable ein.',
      insertPlaceholder1: 'Tippen oder drücken',
      settings: 'Einstellungen',
    },
    questionClassifiers: {
      model: 'Modell',
      inputVars: 'Eingabevariablen',
      outputVars: {
        className: 'Klassennamen',
        usage: 'Nutzungsinformationen des Modells',
      },
      class: 'Klasse',
      classNamePlaceholder: 'Geben Sie Ihren Klassennamen ein',
      advancedSetting: 'Erweiterte Einstellung',
      topicName: 'Themenname',
      topicPlaceholder: 'Geben Sie Ihren Themennamen ein',
      addClass: 'Klasse hinzufügen',
      instruction: 'Anweisung',
      instructionTip: 'Geben Sie zusätzliche Anweisungen ein, um dem Fragenklassifizierer zu helfen, besser zu verstehen, wie Fragen kategorisiert werden sollen.',
      instructionPlaceholder: 'Geben Sie Ihre Anweisung ein',
    },
    parameterExtractor: {
      inputVar: 'Eingabevariable',
      outputVars: {
        isSuccess: 'Ist Erfolg. Bei Erfolg beträgt der Wert 1, bei Misserfolg beträgt der Wert 0.',
        errorReason: 'Fehlergrund',
        usage: 'Nutzungsinformationen des Modells',
      },
      extractParameters: 'Parameter extrahieren',
      importFromTool: 'Aus Tools importieren',
      addExtractParameter: 'Extraktionsparameter hinzufügen',
      addExtractParameterContent: {
        name: 'Name',
        namePlaceholder: 'Name des Extraktionsparameters',
        type: 'Typ',
        typePlaceholder: 'Typ des Extraktionsparameters',
        description: 'Beschreibung',
        descriptionPlaceholder: 'Beschreibung des Extraktionsparameters',
        required: 'Erforderlich',
        requiredContent: 'Erforderlich wird nur als Referenz für die Modellschlussfolgerung verwendet und nicht für die zwingende Validierung der Parameter-Ausgabe.',
      },
      extractParametersNotSet: 'Extraktionsparameter nicht eingerichtet',
      instruction: 'Anweisung',
      instructionTip: 'Geben Sie zusätzliche Anweisungen ein, um dem Parameter-Extraktor zu helfen, zu verstehen, wie Parameter extrahiert werden.',
      advancedSetting: 'Erweiterte Einstellung',
      reasoningMode: 'Schlussfolgerungsmodus',
      reasoningModeTip: 'Sie können den entsprechenden Schlussfolgerungsmodus basierend auf der Fähigkeit des Modells wählen, auf Anweisungen zur Funktionsaufruf- oder Eingabeaufforderungen zu reagieren.',
    },
    iteration: {
      deleteTitle: 'Iterationsknoten löschen?',
      deleteDesc: 'Das Löschen des Iterationsknotens löscht alle untergeordneten Knoten',
      input: 'Eingabe',
      output: 'Ausgabevariablen',
      iteration_one: '{{count}} Iteration',
      iteration_other: '{{count}} Iterationen',
      currentIteration: 'Aktuelle Iteration',
      ErrorMethod: {
        operationTerminated: 'beendet',
        removeAbnormalOutput: 'remove-abnormale_ausgabe',
        continueOnError: 'Fehler "Fortfahren bei"',
      },
      MaxParallelismTitle: 'Maximale Parallelität',
      parallelMode: 'Paralleler Modus',
      errorResponseMethod: 'Methode der Fehlerantwort',
      error_one: '{{Anzahl}} Fehler',
      error_other: '{{Anzahl}} Irrtümer',
      MaxParallelismDesc: 'Die maximale Parallelität wird verwendet, um die Anzahl der Aufgaben zu steuern, die gleichzeitig in einer einzigen Iteration ausgeführt werden.',
      parallelPanelDesc: 'Im parallelen Modus unterstützen Aufgaben in der Iteration die parallele Ausführung.',
      parallelModeEnableDesc: 'Im parallelen Modus unterstützen Aufgaben innerhalb von Iterationen die parallele Ausführung. Sie können dies im Eigenschaftenbereich auf der rechten Seite konfigurieren.',
      answerNodeWarningDesc: 'Warnung im parallelen Modus: Antwortknoten, Zuweisungen von Konversationsvariablen und persistente Lese-/Schreibvorgänge innerhalb von Iterationen können Ausnahmen verursachen.',
      parallelModeEnableTitle: 'Paralleler Modus aktiviert',
      parallelModeUpper: 'PARALLELER MODUS',
      comma: ',',
    },
    note: {
      editor: {
        strikethrough: 'Durchgestrichen',
        large: 'Groß',
        bulletList: 'Aufzählung',
        italic: 'Kursiv',
        small: 'Klein',
        bold: 'Kühn',
        placeholder: 'Schreiben Sie Ihre Notiz...',
        openLink: 'Offen',
        showAuthor: 'Autor anzeigen',
        medium: 'Mittel',
        unlink: 'Trennen',
        link: 'Verbinden',
        enterUrl: 'URL eingeben...',
        invalidUrl: 'Ungültige URL',
      },
      addNote: 'Notiz hinzufügen',
    },
    docExtractor: {
      outputVars: {
        text: 'Extrahierter Text',
      },
      supportFileTypes: 'Unterstützte Dateitypen: {{types}}.',
      inputVar: 'Eingabevariable',
      learnMore: 'Weitere Informationen',
    },
    listFilter: {
      outputVars: {
        first_record: 'Erste Aufnahme',
        result: 'Ergebnis filtern',
        last_record: 'Letzter Datensatz',
      },
      asc: 'ASC',
      limit: 'Top N',
      desc: 'DESC',
      orderBy: 'Sortieren nach',
      inputVar: 'Eingabevariable',
      filterConditionComparisonOperator: 'Operator für den Bedingungsvergleich filtern',
      filterConditionComparisonValue: 'Wert der Filterbedingung',
      filterConditionKey: 'Bedingungsschlüssel filtern',
      filterCondition: 'Filter-Bedingung',
      selectVariableKeyPlaceholder: 'Untervariablenschlüssel auswählen',
      extractsCondition: 'Extrahieren des N-Elements',
    },
    agent: {
      strategy: {
        configureTipDesc: 'Nach der Konfiguration der agentischen Strategie lädt dieser Knoten automatisch die verbleibenden Konfigurationen. Die Strategie wirkt sich auf den Mechanismus des mehrstufigen Tool-Reasoning aus.',
        shortLabel: 'Strategie',
        tooltip: 'Unterschiedliche Agentenstrategien bestimmen, wie das System mehrstufige Werkzeugaufrufe plant und ausführt',
        configureTip: 'Bitte konfigurieren Sie die Agentenstrategie.',
        selectTip: 'Agentische Strategie auswählen',
        searchPlaceholder: 'Agentenstrategie suchen',
        label: 'Agentische Strategie',
      },
      pluginInstaller: {
        install: 'Installieren',
        installing: 'Installation',
      },
      modelNotInMarketplace: {
        desc: 'Dieses Modell wird aus dem lokalen oder GitHub-Repository installiert. Bitte nach der Installation verwenden.',
        manageInPlugins: 'In Plugins verwalten',
        title: 'Modell nicht installiert',
      },
      modelNotSupport: {
        descForVersionSwitch: 'Die installierte Plugin-Version stellt dieses Modell nicht zur Verfügung. Klicken Sie hier, um die Version zu wechseln.',
        desc: 'Die installierte Plugin-Version stellt dieses Modell nicht zur Verfügung.',
        title: 'Nicht unterstütztes Modell',
      },
      modelSelectorTooltips: {
        deprecated: 'Dieses Modell ist veraltet',
      },
      outputVars: {
        files: {
          type: 'Art der Unterstützung. Jetzt nur noch Image unterstützen',
          url: 'Bild-URL',
          title: 'Vom Agenten generierte Dateien',
          upload_file_id: 'Datei-ID hochladen',
          transfer_method: 'Übertragungsmethode. Wert ist remote_url oder local_file',
        },
        text: 'Von Agenten generierte Inhalte',
        json: 'Vom Agenten generiertes JSON',
      },
      checkList: {
        strategyNotSelected: 'Strategie nicht ausgewählt',
      },
      installPlugin: {
        cancel: 'Abbrechen',
        desc: 'Über die Installation des folgenden Plugins',
        changelog: 'Änderungsprotokoll',
        title: 'Plugin installieren',
        install: 'Installieren',
      },
      modelNotSelected: 'Modell nicht ausgewählt',
      modelNotInstallTooltip: 'Dieses Modell ist nicht installiert',
      strategyNotFoundDesc: 'Die installierte Plugin-Version bietet diese Strategie nicht.',
      unsupportedStrategy: 'Nicht unterstützte Strategie',
      toolNotInstallTooltip: '{{tool}} ist nicht installiert',
      notAuthorized: 'Nicht autorisiert',
      pluginNotInstalled: 'Dieses Plugin ist nicht installiert',
      toolbox: 'Werkzeugkasten',
      toolNotAuthorizedTooltip: '{{Werkzeug}} Nicht autorisiert',
      maxIterations: 'Max. Iterationen',
      model: 'Modell',
      strategyNotInstallTooltip: '{{strategy}} ist nicht installiert',
      pluginNotInstalledDesc: 'Dieses Plugin wird von GitHub installiert. Bitte gehen Sie zu Plugins, um sie neu zu installieren',
      strategyNotSet: 'Agentische Strategie nicht festgelegt',
      strategyNotFoundDescAndSwitchVersion: 'Die installierte Plugin-Version bietet diese Strategie nicht. Klicken Sie hier, um die Version zu wechseln.',
      tools: 'Werkzeuge',
      pluginNotFoundDesc: 'Dieses Plugin wird von GitHub installiert. Bitte gehen Sie zu Plugins, um sie neu zu installieren',
      learnMore: 'Weitere Informationen',
      configureModel: 'Modell konfigurieren',
      linkToPlugin: 'Link zu Plugins',
      parameterSchema: 'Parameter-Schema',
      clickToViewParameterSchema: 'Klicken Sie hier, um das Parameterschema anzuzeigen.',
    },
    loop: {
      ErrorMethod: {
        removeAbnormalOutput: 'Abnormale Ausgaben entfernen',
        continueOnError: 'Fortfahren bei Fehler',
        operationTerminated: 'Beendet',
      },
      comma: ',',
      loopNode: 'Schleifen-Knoten',
      loop_other: '{{count}} Schleifen',
      totalLoopCount: 'Gesamtanzahl der Schleifen: {{count}}',
      deleteDesc: 'Das Löschen des Schleifen-Knotens entfernt alle untergeordneten Knoten.',
      loopVariables: 'Schleifenvariablen',
      loop_one: '{{count}} Schleife',
      breakCondition: 'Schleifenbeendigungsbedingung',
      setLoopVariables: 'Setze Variablen innerhalb des Schleifenbereichs',
      breakConditionTip: 'Nur Variablen innerhalb von Schleifen mit Abbruchbedingungen und Konversationsvariablen können referenziert werden.',
      loopMaxCountError: 'Bitte geben Sie eine gültige maximale Schleifenanzahl ein, die von 1 bis {{maxCount}} reicht.',
      deleteTitle: 'Schleifen-Knoten löschen?',
      currentLoop: 'Aktueller Loop',
      loopMaxCount: 'Maximale Schleifenanzahl',
      finalLoopVariables: 'Endgültige Schleifenvariablen',
      exitConditionTip: 'Ein Schleifen-Knoten benötigt mindestens eine Ausgangsbedingung.',
      errorResponseMethod: 'Fehlerantwortmethode',
      initialLoopVariables: 'Ursprüngliche Schleifenvariablen',
      variableName: 'Variablenname',
      error_one: '{{count}} Fehler',
      currentLoopCount: 'Aktuelle Schleifenanzahl: {{count}}',
      inputMode: 'Eingabemodus',
      error_other: '{{count}} Fehler',
      output: 'Ausgabewert',
      input: 'Eingabe',
    },
  },
  tracing: {
    stopBy: 'Gestoppt von {{user}}',
  },
  variableReference: {
    noAvailableVars: 'Keine verfügbaren Variablen',
    conversationVars: 'Konversations-Variablen',
    noAssignedVars: 'Keine verfügbaren zugewiesenen Variablen',
    noVarsForOperation: 'Es stehen keine Variablen für die Zuweisung mit der ausgewählten Operation zur Verfügung.',
    assignedVarsDescription: 'Zugewiesene Variablen müssen beschreibbare Variablen sein, z. B.',
  },
  versionHistory: {
    filter: {
      all: 'Alle',
      onlyShowNamedVersions: 'Nur benannte Versionen anzeigen',
      onlyYours: 'Nur dein',
      reset: 'Filter zurücksetzen',
      empty: 'Kein passendes Versionsprotokoll gefunden.',
    },
    editField: {
      releaseNotesLengthLimit: 'Die Versionshinweise dürfen {{limit}} Zeichen nicht überschreiten.',
      titleLengthLimit: 'Der Titel darf {{limit}} Zeichen nicht überschreiten.',
      releaseNotes: 'Versionshinweise',
      title: 'Titel',
    },
    action: {
      restoreFailure: 'Wiederherstellung der Version fehlgeschlagen',
      updateSuccess: 'Version aktualisiert',
      deleteSuccess: 'Version gelöscht',
      deleteFailure: 'Version löschen fehlgeschlagen',
      restoreSuccess: 'Version wiederhergestellt',
      updateFailure: 'Aktualisierung der Version fehlgeschlagen',
      copyIdSuccess: 'ID in die Zwischenablage kopiert',
    },
    latest: 'Neueste',
    nameThisVersion: 'Nennen Sie diese Version',
    currentDraft: 'Aktueller Entwurf',
    releaseNotesPlaceholder: 'Beschreibe, was sich geändert hat.',
    defaultName: 'Unbetitelte Version',
    title: 'Versionen',
    editVersionInfo: 'Versionsinformationen bearbeiten',
    deletionTip: 'Die Löschung ist unumkehrbar, bitte bestätigen Sie.',
    restorationTip: 'Nach der Wiederherstellung der Version wird der aktuelle Entwurf überschrieben.',
    copyId: 'ID kopieren',
  },
  debug: {
    noData: {
      runThisNode: 'Führe diesen Knoten aus',
      description: 'Die Ergebnisse des letzten Laufs werden hier angezeigt.',
    },
    variableInspect: {
      trigger: {
        normal: 'Variable untersuchen',
        stop: 'Halt an',
        running: 'Caching-Betriebsstatus',
        clear: 'Klar',
        cached: 'Cached-Variablen anzeigen',
      },
      title: 'Variable untersuchen',
      clearAll: 'Alles zurücksetzen',
      emptyLink: 'Erfahren Sie mehr',
      view: 'Protokoll anzeigen',
      systemNode: 'System',
      edited: 'Bearbeitet',
      clearNode: 'Cache-Variable löschen',
      envNode: 'Umwelt',
      chatNode: 'Gespräch',
      resetConversationVar: 'Setze die Gesprächsvariable auf den Standardwert zurück',
      reset: 'Auf den letzten Ausführungswert zurücksetzen',
      emptyTip: 'Nachdem Sie einen Knoten auf der Leinwand durchlaufen oder einen Knoten Schritt für Schritt ausgeführt haben, können Sie den aktuellen Wert der Knotenvariable in der Variableninspektion anzeigen.',
    },
    settingsTab: 'Einstellungen',
    lastRunTab: 'Letzte Ausführung',
    relations: {
      dependents: 'Angehörige',
      dependenciesDescription: 'Knoten, auf die sich dieser Knoten stützt',
      dependencies: 'Abhängigkeiten',
      noDependencies: 'Keine Abhängigkeiten',
      dependentsDescription: 'Knoten, die auf diesem Knoten basieren',
      noDependents: 'Keine Angehörigen',
    },
    relationsTab: 'Beziehungen',
    copyLastRun: 'Letzte Ausführung kopieren',
    copyLastRunError: 'Fehler beim Kopieren der letzten Lauf-Eingaben',
    noMatchingInputsFound: 'Keine übereinstimmenden Eingaben aus dem letzten Lauf gefunden.',
    noLastRunFound: 'Kein vorheriger Lauf gefunden',
    lastOutput: 'Letzte Ausgabe',
  },
}

export default translation
