const translation = {
  steps: {
    header: {
      fallbackRoute: 'ナレッジベース',
    },
    one: 'データソース',
    two: 'テキスト進行中',
    three: '実行と完成',
  },
  error: {
    unavailable: 'このナレッジベースは利用できません',
  },
  firecrawl: {
    configFirecrawl: '🔥Firecrawl の設定',
    apiKeyPlaceholder: 'firecrawl.dev からの API キー',
    getApiKeyLinkText: 'firecrawl.dev から API キーを取得する',
  },
  jinaReader: {
    getApiKeyLinkText: '無料の API キーを jina.ai で取得',
    apiKeyPlaceholder: 'jina.ai からの API キー',
    configJinaReader: 'Jina Reader の設定',
  },
  stepOne: {
    filePreview: 'ファイルプレビュー',
    pagePreview: 'ページプレビュー',
    dataSourceType: {
      file: 'テキストファイルからインポート',
      notion: 'Notion から同期',
      web: 'ウェブサイトから同期',
    },
    uploader: {
      title: 'テキストファイルをアップロード',
      button: 'ファイルまたはフォルダをドラッグアンドドロップする',
      buttonSingleFile: 'ファイルをドラッグアンドドロップする',
      browse: '参照',
      tip: '{{supportTypes}}をサポートしています。1 つあたりの最大サイズは{{size}}MB です。',
      validation: {
        typeError: 'サポートされていないファイルタイプです',
        size: 'ファイルサイズが大きすぎます。最大サイズは{{size}}MB です',
        count: '複数のファイルはサポートされていません',
        filesNumber: 'バッチアップロードの制限（{{filesNumber}}個）に達しました。',
      },
      cancel: 'キャンセル',
      change: '変更',
      failed: 'アップロードに失敗しました',
    },
    notionSyncTitle: 'Notion が接続されていません',
    notionSyncTip: 'Notion と同期するには、まず Notion への接続が必要です。',
    connect: '接続する',
    cancel: 'キャンセル',
    button: '次へ',
    emptyDatasetCreation: '空のナレッジベースを作成します',
    modal: {
      title: '空のナレッジベースを作成',
      tip: '空のナレッジベースにはドキュメントが含まれず、いつでもドキュメントをアップロードできます。',
      input: 'ナレッジベースの名称',
      placeholder: '入力してください',
      nameNotEmpty: '名前は空にできません',
      nameLengthInvalid: '名前は 1〜40 文字である必要があります',
      cancelButton: 'キャンセル',
      confirmButton: '作成',
      failed: '作成に失敗しました',
    },
    website: {
      chooseProvider: 'プロバイダーを選択する',
      fireCrawlNotConfigured: 'Firecrawl が設定されていません',
      fireCrawlNotConfiguredDescription: 'Firecrawl を使用するには、Firecrawl の API キーを設定してください。',
      jinaReaderNotConfigured: 'Jina Reader が設定されていません',
      jinaReaderNotConfiguredDescription: '無料の API キーを入力して、Jina Reader を設定します。',
      configure: '設定',
      configureFirecrawl: '配置 Firecrawl',
      configureJinaReader: '配置 Jina Reader',
      run: '実行',
      firecrawlTitle: '🔥Firecrawl を使っでウエブコンテンツを抽出',
      firecrawlDoc: 'Firecrawl ドキュメント',
      jinaReaderTitle: 'サイト全体を Markdown に変換する',
      jinaReaderDoc: 'Jina Reader の詳細',
      jinaReaderDocLink: 'https://jina.ai/reader',
      useSitemap: 'sitemap(サイトマップ) を使用する',
      useSitemapTooltip: 'サイトマップに沿ってサイトをクロールします。そうでない場合、Jina Reader はページの関連性に基づいて繰り返しクロールし、ページ数は少なくなりますが、高品質のページが得られます。',
      options: 'オプション',
      crawlSubPage: 'サブページをクロールする',
      limit: '制限',
      maxDepth: '最大深度',
      excludePaths: 'パスを除外する',
      includeOnlyPaths: 'パスのみを含める',
      extractOnlyMainContent: 'メインコンテンツのみを抽出する (ヘッダー、ナビ、フッターなどは抽出しない)',
      exceptionErrorTitle: 'Firecrawl ジョブの実行中に例外が発生しました：',
      unknownError: '不明なエラー',
      totalPageScraped: 'スクレイピングされた総ページ数：',
      selectAll: 'すべて選択',
      resetAll: 'すべてリセット',
      scrapTimeInfo: '{{time}} 秒以内に合計 {{total}} ページをスクレイピングしました',
      preview: 'プレビュー',
      maxDepthTooltip: '入力された URL を基にしたクローリング作業での設定可能な最大深度について説明します。深度 0 は入力された URL 自体のページを対象としたスクレイピングを意味します。深度 1 では、元の URL の直下にあるページ（URL に続く最初の"/"以降の内容）もスクレイピングの対象になります。この深度は指定した数値まで増加させることができ、それに応じてスクレイピングの範囲も広がっていきます。',
      waterCrawlNotConfiguredDescription: 'API キーを使って Watercrawl を設定します。',
      configureWatercrawl: 'ウォータークローラーを設定する',
      watercrawlDoc: 'ウォータークローリングの文書',
      watercrawlTitle: 'Watercrawl を使用してウェブコンテンツを抽出する',
      waterCrawlNotConfigured: 'Watercrawl は設定されていません',
    },
  },
  stepTwo: {
    segmentation: 'チャンク設定',
    auto: '自動',
    autoDescription: 'チャンクと前処理ルールを自動的に設定します。初めてのユーザーはこれを選択することをおすすめします。',
    custom: 'カスタム',
    customDescription: 'チャンクのルール、チャンクの長さ、前処理ルールなどをカスタマイズします。',
    general: '汎用',
    generalTip: '汎用テキスト分割モードです。検索とコンテキスト抽出に同じチャンクを使用します。',
    parentChild: '親子',
    parentChildTip: '親子分割モード (階層分割モード) では、子チャンクを検索に、親チャンクをコンテキスト抽出に使用します。',
    parentChunkForContext: 'コンテキスト用親チャンク',
    childChunkForRetrieval: '検索用子チャンク',
    paragraph: '段落',
    paragraphTip: '区切り文字と最大チャンク長に基づいてテキストを段落に分割し、分割されたテキストを検索用の親チャンクとして使用します。',
    fullDoc: '全文',
    fullDocTip: 'ドキュメント全体を親チャンクとして使用し、直接検索します。パフォーマンス上の理由から、10000 トークンを超えるテキストは自動的に切り捨てられます。',
    separator: 'チャンク識別子',
    separatorPlaceholder: '例えば改行（\\\\n）や特殊なセパレータ（例：「***」）',
    maxLength: '最大チャンク長',
    overlap: 'チャンクのオーバーラップ',
    overlapTip: 'チャンクのオーバーラップを設定することで、それらの間の意味的な関連性を維持し、検索効果を向上させることができます。最大チャンクサイズの 10%〜25% を設定することをおすすめします。',
    overlapCheck: 'チャンクのオーバーラップは最大チャンク長を超えてはいけません',
    rules: 'テキストの前処理ルール',
    removeExtraSpaces: '連続するスペース、改行、タブを置換する',
    removeUrlEmails: 'すべての URL とメールアドレスを削除する',
    removeStopwords: '「a」「an」「the」などのストップワードを削除する',
    preview: 'プレビュー',
    previewChunk: 'チャンクをプレビュー',
    reset: 'リセット',
    indexMode: 'インデックス方法',
    qualified: '高品質',
    highQualityTip: '高品質モードで埋め込みを終了したら、経済的モードに戻すことはできません。',
    recommend: '推奨',
    qualifiedTip: '埋め込みモデルを呼び出してドキュメントを処理し、より正確な検索を行うと、LLM が高品質の回答を生成するのに役立ちます。',
    warning: 'モデルプロバイダの API キーを設定してください。',
    click: '設定に移動',
    economical: '経済的',
    economicalTip: '検索時にチャンクあたり 10 個のキーワードを使用することで、精度は低下しますが、トークン消費を抑えられます。',
    QATitle: '質問と回答形式でセグメント化',
    QATip: 'このオプションを有効にすると、追加のトークンが消費されます',
    QALanguage: '使用言語',
    useQALanguage: 'Q&A 形式で分割',
    estimateCost: '見積もり',
    estimateSegment: '推定チャンク数',
    segmentCount: 'チャンク',
    calculating: '計算中...',
    fileSource: 'ドキュメントの前処理',
    notionSource: 'ページの前処理',
    websiteSource: 'ウエブサイドの前処理',
    other: 'その他',
    fileUnit: 'ファイル',
    notionUnit: 'ページ',
    webpageUnit: ' ページ',
    previousStep: '前のステップ',
    nextStep: '保存して処理',
    save: '保存して処理',
    cancel: 'キャンセル',
    sideTipTitle: 'なぜチャンクと前処理が必要なのか',
    sideTipP1: 'テキストデータを処理する際、チャンクとクリーニングは 2 つの重要な前処理ステップです。',
    sideTipP2: 'セグメンテーションは長いテキストを段落に分割し、モデルがより理解しやすくします。これにより、モデルの結果の品質と関連性が向上します。',
    sideTipP3: 'クリーニングは不要な文字や書式を削除し、ナレッジベースをよりクリーンで解析しやすいものにします。',
    sideTipP4: '適切なチャンクとクリーニングはモデルのパフォーマンスを向上させ、より正確で価値のある結果を提供します。',
    previewTitle: 'プレビュー',
    previewTitleButton: 'プレビュー',
    previewButton: 'Q&A 形式に切り替える',
    previewSwitchTipStart: '現在のチャンクプレビューはテキスト形式です。質問と回答形式のプレビューに切り替えると、',
    previewSwitchTipEnd: ' 追加のトークンが消費されます',
    characters: '文字',
    indexSettingTip: 'インデックス方法を変更するには、',
    retrievalSettingTip: '検索方法を変更するには、',
    datasetSettingLink: 'ナレッジベース設定',
    separatorTip: '区切り文字は、テキストを区切るために使用される文字です。\\n\\n と \\n は、段落と行を区切るために一般的に使用される区切り記号です。カンマ (\\n\\n,\\n) と組み合わせると、最大チャンク長を超えると、段落は行で区切られます。自分で定義した特別な区切り文字を使用することもできます (例:***)。',
    maxLengthCheck: 'チャンクの最大長は {{limit}} 未満にする必要があります',
    previewChunkTip: 'プレビューを読み込むには、左側の \'チャンクをプレビュー\' ボタンをクリックしてください',
    previewChunkCount: '推定チャンク数：{{count}}',
    switch: '切り替え',
    qaSwitchHighQualityTipTitle: 'Q&A 形式には高品質なインデックスが必要です',
    qaSwitchHighQualityTipContent: '現在、高品質なインデックス作成のみが Q&A 形式の分割をサポートしています。高品質モードに切り替えますか？',
    notAvailableForParentChild: '親子インデックスでは利用できません',
    notAvailableForQA: 'Q&A インデックスでは利用できません',
    parentChildDelimiterTip: '区切り文字とは、テキストを分割するために使用される文字です。\\n\\n は、元のドキュメントを大きな親チャンクに分割する際におすすめです。独自の区切り文字も使用できます。',
    parentChildChunkDelimiterTip: '区切り文字とは、テキストを分割するために使用される文字です。\\n は、親チャンクを小さな子チャンクに分割する際におすすめです。独自の区切り文字も使用できます。',
  },
  stepThree: {
    creationTitle: '🎉 ナレッジベースが作成されました',
    creationContent: 'ナレッジベースの名前は自動的に設定されましたが、自由に変更できます。',
    label: 'ナレッジベース名',
    additionTitle: '🎉 ドキュメントがアップロードされました',
    additionP1: 'ドキュメントはナレッジベースにアップロードされました',
    additionP2: '、ナレッジベースのドキュメントリストで見つけることができます。',
    stop: '処理を停止',
    resume: '処理を再開',
    navTo: 'ドキュメントに移動',
    sideTipTitle: '次は何ですか',
    sideTipContent: 'ドキュメントのインデックスが完了したら、ナレッジベースをアプリケーションのコンテキストとして統合することができます。プロンプトオーケストレーションページでコンテキスト設定を見つけることができます。また、独立した ChatGPT インデックスプラグインとしてリリースすることもできます。',
    modelTitle: '埋め込みを停止してもよろしいですか？',
    modelContent: '後で処理を再開する必要がある場合は、中断した場所から続行します。',
    modelButtonConfirm: '確認',
    modelButtonCancel: 'キャンセル',
  },
  otherDataSource: {
    title: '他のデータソースと接続しますか？',
    description: '現在、Dify のナレッジベースには利用できるデータソースが限られています。Dify のナレッジベースにデータソースを提供いただくことは、プラットフォームの柔軟性と能力を向上させる上で非常に有益です。貢献ガイドをご用意していますので、ぜひご協力ください。詳細については、以下のリンクをクリックしてください。',
    learnMore: '詳細はこちら',
  },
  watercrawl: {
    getApiKeyLinkText: 'watercrawl.dev から API キーを取得してください。',
    configWatercrawl: 'ウォータークローラーを設定する',
    apiKeyPlaceholder: 'watercrawl.dev からの API キー',
  },
}

export default translation
