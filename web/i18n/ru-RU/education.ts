const translation = {
  toVerifiedTip: {
    end: 'для профессионального плана Dify.',
    front: 'Теперь вы имеете право на статус "Проверенное образование". Пожалуйста, введите свои образовательные данные ниже, чтобы завершить процесс и получить',
    coupon: 'эксклюзивный 100% купон',
  },
  form: {
    schoolName: {
      title: 'Название вашей школы',
      placeholder: 'Введите официальное, полное название вашей школы',
    },
    schoolRole: {
      option: {
        student: 'Студен<PERSON>',
        teacher: 'Учитель',
        administrator: 'Школьный администратор',
      },
      title: 'Ваша школьная роль',
    },
    terms: {
      desc: {
        termsOfService: 'Условия обслуживания',
        front: 'Ваша информация и использование статуса Проверенное образование подлежат нашим',
        privacyPolicy: 'Политика конфиденциальности',
        and: 'и',
        end: '. Отправляя:',
      },
      option: {
        age: 'Я подтверждаю, что мне не меньше 18 лет',
        inSchool: 'Я подтверждаю, что я зачислен или работаю в указанной учреждении. Dify может запросить подтверждение зачисления/трудоустройства. Если я неправильно укажу свою правообладанность, я согласен оплатить любые сборы, которые изначально были отменены на основании моего образовательного статуса.',
      },
      title: 'Условия и соглашения',
    },
  },
  submit: 'Отправить',
  rejectTitle: 'Ваша образовательная проверка Dify была отклонена',
  currentSigned: 'В ДАННЫЙ МОМЕНТ ВХОД В ПРОФИЛЬ КАК',
  toVerified: 'Получите подтверждение образования',
  learn: 'Узнайте, как получить подтверждение образования',
  submitError: 'Отправка формы не удалась. Пожалуйста, попробуйте позже.',
  successTitle: 'Вы получили подтвержденное образование Dify',
  emailLabel: 'Ваш текущий адрес электронной почты',
  rejectContent: 'К сожалению, вы не имеете права на статус Проверенного образованием и, следовательно, не можете получить эксклюзивный купон на 100% для профессионального плана Dify, если вы используете этот адрес электронной почты.',
  successContent: 'Мы выдали купон на 100% скидку на план Dify Professional для вашего аккаунта. Купон действителен в течение одного года, пожалуйста, используйте его в течение срока действия.',
  notice: {
    expired: {
      summary: {
        line1: 'Вы все еще можете получить доступ к Dify и использовать его.',
        line2: 'Однако вы больше не имеете права на новые купоны на скидку на образование.',
      },
      title: 'Ваш статус образования истек',
    },
    isAboutToExpire: {
      summary: 'Не волнуйся — это не повлияет на твою текущую подписку, но ты не получишь образовательную скидку при её продлении, если не подтвердишь свой статус снова.',
    },
    stillInEducation: {
      title: 'Все еще учишься?',
      expired: 'Переутвердите сейчас, чтобы получить новый купон на предстоящий учебный год. Мы добавим его на ваш аккаунт, и вы сможете использовать его для следующего обновления.',
      isAboutToExpire: 'Проверьте еще раз, чтобы получить новый купон на предстоящий учебный год. Он будет сохранен в вашем аккаунте и готов к использованию при следующем продлении.',
    },
    alreadyGraduated: {
      title: 'Уже окончили?',
      expired: 'Не стесняйтесь обновить подписку в любое время, чтобы получить полный доступ к платным функциям.',
      isAboutToExpire: 'Ваша текущая подписка останется активной. Когда она закончится, вы перейдете на план Sandbox, или вы можете в любое время обновить подписку, чтобы восстановить полный доступ к платным функциям.',
    },
    action: {
      upgrade: 'Обновление',
      reVerify: 'Перепроверить',
      dismiss: 'Отклонить',
    },
    dateFormat: 'ДД/ММ/ГГГГ',
  },
}

export default translation
