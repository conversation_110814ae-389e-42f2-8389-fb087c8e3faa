const translation = {
  title: 'Instrumente',
  createCustomTool: 'Creează Instrument Personalizat',
  type: {
    all: 'Toate',
    builtIn: 'Incorporat',
    custom: 'Personalizat',
    workflow: 'Flux de lucru',
  },
  contribute: {
    line1: 'Sunt interesat să ',
    line2: 'contribui la Dify cu instrumente.',
    viewGuide: 'Vezi ghidul',
  },
  author: 'De',
  auth: {
    authorized: 'Autorizat',
    setup: 'Configurează autorizarea pentru a utiliza',
    setupModalTitle: 'Configurează Autorizarea',
    setupModalTitleDescription: 'Dup<PERSON> configurarea credențialelor, toți membrii din spațiul de lucru pot utiliza acest instrument la orchestrarea aplicațiilor.',
  },
  includeToolNum: '{{num}} instrumente incluse',
  addTool: 'Adaugă Instrument',
  createTool: {
    title: 'Creează Instrument Personalizat',
    editAction: 'Configurează',
    editTitle: 'Editează Instrument Personalizat',
    name: 'Nume',
    toolNamePlaceHolder: 'Introduceți numele instrumentului',
    schema: 'Schema',
    schemaPlaceHolder: 'Introduceți aici schema OpenAPI',
    viewSchemaSpec: 'Vezi specificația OpenAPI-Swagger',
    importFromUrl: 'Importă de la URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Vă rugăm să introduceți un URL valid',
    examples: 'Exemple',
    exampleOptions: {
      json: 'Vreme(JSON)',
      yaml: 'Pet Store(YAML)',
      blankTemplate: 'Șablon Gol',
    },
    availableTools: {
      title: 'Instrumente Disponibile',
      name: 'Nume',
      description: 'Descriere',
      method: 'Metodă',
      path: 'Cale',
      action: 'Acțiuni',
      test: 'Testează',
    },
    authMethod: {
      title: 'Metoda de Autorizare',
      type: 'Tipul de Autorizare',
      keyTooltip: 'Cheie antet HTTP, puteți lăsa "Autorizare" dacă nu știți ce este sau setați-o la o valoare personalizată',
      types: {
        none: 'Niciuna',
        apiKeyPlaceholder: 'Nume antet HTTP pentru cheia API',
        apiValuePlaceholder: 'Introduceți cheia API',
        api_key_header: 'Antet',
        api_key_query: 'Parametru de interogare',
        queryParamPlaceholder: 'Numele parametrului de interogare pentru cheia API',
      },
      key: 'Cheie',
      value: 'Valoare',
      queryParam: 'Parametru de interogare',
      queryParamTooltip: 'Numele parametrului de interogare pentru cheia API care trebuie transmis, de exemplu, "key" în "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Tipul de Autentificare',
      types: {
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Personalizat',
      },
    },
    privacyPolicy: 'Politica de Confidențialitate',
    privacyPolicyPlaceholder: 'Vă rugăm să introduceți politica de confidențialitate',
    deleteToolConfirmTitle: 'Ștergeți această unealtă?',
    deleteToolConfirmContent: ' Ștergerea uneltă este irreversibilă. Utilizatorii nu vor mai putea accesa uneltă dvs.',
    toolInput: {
      methodParameter: 'Parametru',
      description: 'Descriere',
      methodSetting: 'Setare',
      methodSettingTip: 'Utilizatorul completează configurația instrumentului',
      methodParameterTip: 'Completări LLM în timpul inferenței',
      name: 'Nume',
      descriptionPlaceholder: 'Descrierea semnificației parametrului',
      label: 'Tags',
      required: 'Necesar',
      method: 'Metodă',
      title: 'Intrare instrument',
      labelPlaceholder: 'Alegeți etichetele (opțional)',
    },
    descriptionPlaceholder: 'Scurtă descriere a scopului instrumentului, de exemplu, obțineți temperatura pentru o anumită locație.',
    nameForToolCall: 'Numele apelului instrumentului',
    description: 'Descriere',
    confirmTip: 'Aplicațiile care folosesc acest instrument vor fi afectate',
    nameForToolCallPlaceHolder: 'Utilizat pentru recunoașterea mașinii, cum ar fi getCurrentWeather, list_pets',
    customDisclaimer: 'Declinarea responsabilității personalizate',
    confirmTitle: 'Confirmați pentru a salva?',
    customDisclaimerPlaceholder: 'Vă rugăm să introduceți declinarea responsabilității personalizate',
    nameForToolCallTip: 'Acceptă doar numere, litere și caractere de subliniere.',
  },
  test: {
    title: 'Testează',
    parametersValue: 'Parametri & Valoare',
    parameters: 'Parametri',
    value: 'Valoare',
    testResult: 'Rezultate Test',
    testResultPlaceholder: 'Rezultatul testului va fi afișat aici',
  },
  thought: {
    using: 'Utilizând',
    used: 'Utilizat',
    requestTitle: 'Cerere către',
    responseTitle: 'Răspuns de la',
  },
  setBuiltInTools: {
    info: 'Informații',
    setting: 'Setări',
    toolDescription: 'Descriere instrument',
    parameters: 'parametri',
    string: 'șir',
    number: 'număr',
    required: 'Obligatoriu',
    infoAndSetting: 'Informații și Setări',
    file: 'fișier',
  },
  noCustomTool: {
    title: 'Niciun instrument personalizat!',
    content: 'Adăugați și gestionați aici instrumentele personalizate pentru construirea aplicațiilor AI.',
    createTool: 'Creează Instrument',
  },
  noSearchRes: {
    title: 'Ne pare rău, nu s-au găsit rezultate!',
    content: 'Nu am putut găsi niciun instrument care să se potrivească căutării dvs.',
    reset: 'Resetează Căutarea',
  },
  builtInPromptTitle: 'Prompt',
  toolRemoved: 'Instrument eliminat',
  notAuthorized: 'Instrument neautorizat',
  howToGet: 'Cum să obții',
  addToolModal: {
    added: 'adăugat',
    category: 'categorie',
    manageInTools: 'Gestionați în Instrumente',
    add: 'adăuga',
    type: 'tip',
    custom: {
      title: 'Niciun instrument personalizat disponibil',
      tip: 'Creează un instrument personalizat',
    },
    workflow: {
      title: 'Niciun instrument de flux de lucru disponibil',
      tip: 'Publicați fluxuri de lucru ca instrumente în Studio',
    },
    mcp: {
      title: 'Niciun instrument MCP disponibil',
      tip: 'Adăugați un server MCP',
    },
    agent: {
      title: 'Nicio strategie de agent disponibilă',
    },
  },
  openInStudio: 'Deschide în Studio',
  customToolTip: 'Aflați mai multe despre instrumentele personalizate Dify',
  toolNameUsageTip: 'Numele de apel al instrumentului pentru raționamentul și solicitarea agentului',
  copyToolName: 'Copiază numele',
  noTools: 'Nu s-au găsit unelte',
  mcp: {
    create: {
      cardTitle: 'Adăugare Server MCP (HTTP)',
      cardLink: 'Aflați mai multe despre integrarea serverului MCP',
    },
    noConfigured: 'Server Neconfigurat',
    updateTime: 'Actualizat',
    toolsCount: '{count} unelte',
    noTools: 'Nu există unelte disponibile',
    modal: {
      title: 'Adăugare Server MCP (HTTP)',
      editTitle: 'Editare Server MCP (HTTP)',
      name: 'Nume și Pictogramă',
      namePlaceholder: 'Denumiți-vă serverul MCP',
      serverUrl: 'URL Server',
      serverUrlPlaceholder: 'URL către endpoint-ul serverului',
      serverUrlWarning: 'Actualizarea adresei serverului poate întrerupe aplicațiile care depind de acesta',
      serverIdentifier: 'Identificator Server',
      serverIdentifierTip: 'Identificator unic pentru serverul MCP în spațiul de lucru. Doar litere mici, cifre, underscore și cratime. Maxim 24 de caractere.',
      serverIdentifierPlaceholder: 'Identificator unic, ex: my-mcp-server',
      serverIdentifierWarning: 'Serverul nu va fi recunoscut de aplicațiile existente după schimbarea ID-ului',
      cancel: 'Anulare',
      save: 'Salvare',
      confirm: 'Adăugare și Autorizare',
      timeout: 'Timp de așteptare',
      sseReadTimeout: 'Timp de așteptare pentru citirea SSE',
    },
    delete: 'Eliminare Server MCP',
    deleteConfirmTitle: 'Ștergeți {mcp}?',
    operation: {
      edit: 'Editare',
      remove: 'Eliminare',
    },
    authorize: 'Autorizare',
    authorizing: 'Se autorizează...',
    authorizingRequired: 'Autorizare necesară',
    authorizeTip: 'După autorizare, uneltele vor fi afișate aici.',
    update: 'Actualizare',
    updating: 'Se actualizează...',
    gettingTools: 'Se obțin unelte...',
    updateTools: 'Se actualizează unelte...',
    toolsEmpty: 'Unelte neîncărcate',
    getTools: 'Obține unelte',
    toolUpdateConfirmTitle: 'Actualizare Listă Unelte',
    toolUpdateConfirmContent: 'Actualizarea listei de unelte poate afecta aplicațiile existente. Continuați?',
    toolsNum: '{count} unelte incluse',
    onlyTool: '1 unealtă inclusă',
    identifier: 'Identificator Server (Clic pentru Copiere)',
    server: {
      title: 'Server MCP',
      url: 'URL Server',
      reGen: 'Regenerați URL server?',
      addDescription: 'Adăugare descriere',
      edit: 'Editare descriere',
      modal: {
        addTitle: 'Adăugați descriere pentru activarea serverului MCP',
        editTitle: 'Editare descriere',
        description: 'Descriere',
        descriptionPlaceholder: 'Explicați funcționalitatea acestei unelte și cum ar trebui să fie utilizată de LLM',
        parameters: 'Parametri',
        parametersTip: 'Adăugați descrieri pentru fiecare parametru pentru a ajuta LLM să înțeleagă scopul și constrângerile.',
        parametersPlaceholder: 'Scopul și constrângerile parametrului',
        confirm: 'Activare Server MCP',
      },
      publishTip: 'Aplicație nepublicată. Publicați aplicația mai întâi.',
    },
  },
}

export default translation
