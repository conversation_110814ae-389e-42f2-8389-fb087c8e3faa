.option {
  width: 188px;
  height: 128px;
  @apply box-border cursor-pointer bg-auto bg-no-repeat bg-center rounded-md;
}
.active {
  @apply border-[1.5px] border-[#2970FF];
}
.iframeIcon {
  background-image: url(../assets/iframe-option.svg);
}
.scriptsIcon {
  background-image: url(../assets/scripts-option.svg);
}
.chromePluginIcon {
  background-image: url(../assets/chromeplugin-option.svg);
}
.pluginInstallIcon {
  background-image: url(../assets/chromeplugin-install.svg);
}

:global(html[data-theme="dark"]) .iframeIcon,
:global(html[data-theme="dark"]) .scriptsIcon,
:global(html[data-theme="dark"]) .chromePluginIcon {
  filter: invert(0.86) hue-rotate(180deg) saturate(0.5) brightness(0.95);
}

:global(html[data-theme="dark"]) .pluginInstallIcon {
  filter: invert(0.9);
}
