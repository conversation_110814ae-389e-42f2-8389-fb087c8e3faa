/* Attention: Generate by code. Don't update by hand!!! */
html[data-theme="dark"] {
  --color-components-input-bg-normal: rgb(255 255 255 / 0.08);
  --color-components-input-text-placeholder: rgb(200 206 218 / 0.3);
  --color-components-input-bg-hover: rgb(255 255 255 / 0.03);
  --color-components-input-bg-active: rgb(255 255 255 / 0.05);
  --color-components-input-border-active: #747481;
  --color-components-input-border-destructive: #f97066;
  --color-components-input-text-filled: #f4f4f5;
  --color-components-input-bg-destructive: rgb(255 255 255 / 0.01);
  --color-components-input-bg-disabled: rgb(255 255 255 / 0.03);
  --color-components-input-text-disabled: rgb(200 206 218 / 0.3);
  --color-components-input-text-filled-disabled: rgb(200 206 218 / 0.6);
  --color-components-input-border-hover: #3a3a40;
  --color-components-input-border-active-prompt-1: #36bffa;
  --color-components-input-border-active-prompt-2: #296dff;

  --color-components-kbd-bg-gray: rgb(255 255 255 / 0.03);
  --color-components-kbd-bg-white: rgb(255 255 255 / 0.12);

  --color-components-tooltip-bg: rgb(24 24 27 / 0.95);

  --color-components-button-primary-text: rgb(255 255 255 / 0.95);
  --color-components-button-primary-bg: #155aef;
  --color-components-button-primary-border: rgb(255 255 255 / 0.12);
  --color-components-button-primary-bg-hover: #296dff;
  --color-components-button-primary-border-hover: rgb(255 255 255 / 0.2);
  --color-components-button-primary-bg-disabled: rgb(255 255 255 / 0.03);
  --color-components-button-primary-border-disabled: rgb(255 255 255 / 0.08);
  --color-components-button-primary-text-disabled: rgb(255 255 255 / 0.2);

  --color-components-button-secondary-text: rgb(255 255 255 / 0.8);
  --color-components-button-secondary-text-disabled: rgb(255 255 255 / 0.2);
  --color-components-button-secondary-bg: rgb(255 255 255 / 0.12);
  --color-components-button-secondary-bg-hover: rgb(255 255 255 / 0.2);
  --color-components-button-secondary-bg-disabled: rgb(255 255 255 / 0.03);
  --color-components-button-secondary-border: rgb(255 255 255 / 0.08);
  --color-components-button-secondary-border-hover: rgb(255 255 255 / 0.12);
  --color-components-button-secondary-border-disabled: rgb(255 255 255 / 0.05);

  --color-components-button-tertiary-text: #d9d9de;
  --color-components-button-tertiary-text-disabled: rgb(255 255 255 / 0.2);
  --color-components-button-tertiary-bg: rgb(255 255 255 / 0.08);
  --color-components-button-tertiary-bg-hover: rgb(255 255 255 / 0.12);
  --color-components-button-tertiary-bg-disabled: rgb(255 255 255 / 0.03);

  --color-components-button-ghost-text: #d9d9de;
  --color-components-button-ghost-text-disabled: rgb(255 255 255 / 0.2);
  --color-components-button-ghost-bg-hover: rgb(200 206 218 / 0.08);

  --color-components-button-destructive-primary-text: rgb(255 255 255 / 0.95);
  --color-components-button-destructive-primary-text-disabled: rgb(255 255 255 / 0.2);
  --color-components-button-destructive-primary-bg: #d92d20;
  --color-components-button-destructive-primary-bg-hover: #f04438;
  --color-components-button-destructive-primary-bg-disabled: rgb(240 68 56 / 0.14);
  --color-components-button-destructive-primary-border: rgb(255 255 255 / 0.12);
  --color-components-button-destructive-primary-border-hover: rgb(255 255 255 / 0.2);
  --color-components-button-destructive-primary-border-disabled: rgb(255 255 255 / 0.08);

  --color-components-button-destructive-secondary-text: #f97066;
  --color-components-button-destructive-secondary-text-disabled: rgb(240 68 56 / 0.2);
  --color-components-button-destructive-secondary-bg: rgb(255 255 255 / 0.12);
  --color-components-button-destructive-secondary-bg-hover: rgb(240 68 56 / 0.14);
  --color-components-button-destructive-secondary-bg-disabled: rgb(240 68 56 / 0.08);
  --color-components-button-destructive-secondary-border: rgb(255 255 255 / 0.08);
  --color-components-button-destructive-secondary-border-hover: rgb(255 255 255 / 0.12);
  --color-components-button-destructive-secondary-border-disabled: rgb(240 68 56 / 0.08);

  --color-components-button-destructive-tertiary-text: #f97066;
  --color-components-button-destructive-tertiary-text-disabled: rgb(240 68 56 / 0.2);
  --color-components-button-destructive-tertiary-bg: rgb(240 68 56 / 0.14);
  --color-components-button-destructive-tertiary-bg-hover: rgb(240 68 56 / 0.25);
  --color-components-button-destructive-tertiary-bg-disabled: rgb(240 68 56 / 0.08);

  --color-components-button-destructive-ghost-text: #f97066;
  --color-components-button-destructive-ghost-text-disabled: rgb(240 68 56 / 0.2);
  --color-components-button-destructive-ghost-bg-hover: rgb(240 68 56 / 0.14);

  --color-components-button-secondary-accent-text: rgb(255 255 255 / 0.8);
  --color-components-button-secondary-accent-text-disabled: rgb(255 255 255 / 0.2);
  --color-components-button-secondary-accent-bg: rgb(255 255 255 / 0.05);
  --color-components-button-secondary-accent-bg-hover: rgb(255 255 255 / 0.08);
  --color-components-button-secondary-accent-bg-disabled: rgb(255 255 255 / 0.03);
  --color-components-button-secondary-accent-border: rgb(255 255 255 / 0.08);
  --color-components-button-secondary-accent-border-hover: rgb(255 255 255 / 0.12);
  --color-components-button-secondary-accent-border-disabled: rgb(255 255 255 / 0.05);

  --color-components-button-indigo-bg: #444ce7;
  --color-components-button-indigo-bg-hover: #6172f3;
  --color-components-button-indigo-bg-disabled: rgb(255 255 255 / 0.03);

  --color-components-checkbox-icon: rgb(255 255 255 / 0.95);
  --color-components-checkbox-icon-disabled: rgb(255 255 255 / 0.2);
  --color-components-checkbox-bg: #296dff;
  --color-components-checkbox-bg-hover: #5289ff;
  --color-components-checkbox-bg-disabled: rgb(255 255 255 / 0.03);
  --color-components-checkbox-border: rgb(255 255 255 / 0.4);
  --color-components-checkbox-border-hover: rgb(255 255 255 / 0.6);
  --color-components-checkbox-border-disabled: rgb(255 255 255 / 0.01);
  --color-components-checkbox-bg-unchecked: rgb(255 255 255 / 0.03);
  --color-components-checkbox-bg-unchecked-hover: rgb(255 255 255 / 0.05);
  --color-components-checkbox-bg-disabled-checked: rgb(21 90 239 / 0.2);

  --color-components-radio-border-checked: #296dff;
  --color-components-radio-border-checked-hover: #5289ff;
  --color-components-radio-border-checked-disabled: rgb(21 90 239 / 0.2);
  --color-components-radio-bg-disabled: rgb(255 255 255 / 0.03);
  --color-components-radio-border: rgb(255 255 255 / 0.4);
  --color-components-radio-border-hover: rgb(255 255 255 / 0.6);
  --color-components-radio-border-disabled: rgb(255 255 255 / 0.01);
  --color-components-radio-bg: rgb(255 255 255 / 0);
  --color-components-radio-bg-hover: rgb(255 255 255 / 0.05);

  --color-components-toggle-knob: #f4f4f5;
  --color-components-toggle-knob-disabled: rgb(255 255 255 / 0.2);
  --color-components-toggle-bg: #296dff;
  --color-components-toggle-bg-hover: #5289ff;
  --color-components-toggle-bg-disabled: rgb(255 255 255 / 0.08);
  --color-components-toggle-bg-unchecked: rgb(255 255 255 / 0.2);
  --color-components-toggle-bg-unchecked-hover: rgb(255 255 255 / 0.3);
  --color-components-toggle-bg-unchecked-disabled: rgb(255 255 255 / 0.08);
  --color-components-toggle-knob-hover: #fefefe;

  --color-components-card-bg: #222225;
  --color-components-card-border: rgb(255 255 255 / 0.03);
  --color-components-card-bg-alt: #27272b;
  --color-components-card-bg-transparent: rgb(34 34 37 / 0);
  --color-components-card-bg-alt-transparent: rgb(39 39 43 / 0);

  --color-components-menu-item-text: rgb(200 206 218 / 0.6);
  --color-components-menu-item-text-active: rgb(255 255 255 / 0.95);
  --color-components-menu-item-text-hover: rgb(200 206 218 / 0.8);
  --color-components-menu-item-text-active-accent: rgb(255 255 255 / 0.95);
  --color-components-menu-item-bg-active: rgb(200 206 218 / 0.14);
  --color-components-menu-item-bg-hover: rgb(200 206 218 / 0.08);

  --color-components-panel-bg: #222225;
  --color-components-panel-bg-blur: rgb(44 44 48 / 0.95);
  --color-components-panel-border: rgb(200 206 218 / 0.14);
  --color-components-panel-border-subtle: rgb(200 206 218 / 0.08);
  --color-components-panel-gradient-2: #222225;
  --color-components-panel-gradient-1: #27272b;
  --color-components-panel-bg-alt: #222225;
  --color-components-panel-on-panel-item-bg: #27272b;
  --color-components-panel-on-panel-item-bg-hover: #3a3a40;
  --color-components-panel-on-panel-item-bg-alt: #3a3a40;
  --color-components-panel-on-panel-item-bg-transparent: rgb(44 44 48 / 0.95);
  --color-components-panel-on-panel-item-bg-hover-transparent: rgb(58 58 64 / 0);
  --color-components-panel-on-panel-item-bg-destructive-hover-transparent: rgb(255 251 250 / 0);

  --color-components-panel-bg-transparent: rgb(34 34 37 / 0);

  --color-components-main-nav-nav-button-text: rgb(200 206 218 / 0.6);
  --color-components-main-nav-nav-button-text-active: #f4f4f5;
  --color-components-main-nav-nav-button-bg: rgb(255 255 255 / 0);
  --color-components-main-nav-nav-button-bg-active: rgb(200 206 218 / 0.14);
  --color-components-main-nav-nav-button-border: rgb(255 255 255 / 0.08);
  --color-components-main-nav-nav-button-bg-hover: rgb(200 206 218 / 0.04);

  --color-components-main-nav-nav-user-border: rgb(255 255 255 / 0.05);

  --color-components-slider-knob: #f4f4f5;
  --color-components-slider-knob-hover: #fefefe;
  --color-components-slider-knob-disabled: rgb(255 255 255 / 0.2);
  --color-components-slider-range: #296dff;
  --color-components-slider-track: rgb(255 255 255 / 0.2);
  --color-components-slider-knob-border-hover: rgb(16 24 40 / 0.3);
  --color-components-slider-knob-border: rgb(16 24 40 / 0.2);

  --color-components-segmented-control-item-active-bg: rgb(255 255 255 / 0.08);
  --color-components-segmented-control-item-active-border: rgb(200 206 218 / 0.08);
  --color-components-segmented-control-bg-normal: rgb(24 24 27 / 0.7);
  --color-components-segmented-control-item-active-accent-bg: rgb(21 90 239 / 0.2);
  --color-components-segmented-control-item-active-accent-border: rgb(21 90 239 / 0.3);

  --color-components-option-card-option-bg: rgb(200 206 218 / 0.04);
  --color-components-option-card-option-selected-bg: rgb(255 255 255 / 0.05);
  --color-components-option-card-option-selected-border: #5289ff;
  --color-components-option-card-option-border: rgb(200 206 218 / 0.2);
  --color-components-option-card-option-bg-hover: rgb(200 206 218 / 0.14);
  --color-components-option-card-option-border-hover: rgb(200 206 218 / 0.3);

  --color-components-tab-active: #296dff;

  --color-components-badge-white-to-dark: rgb(24 24 27 / 0.8);
  --color-components-badge-status-light-success-bg: #17b26a;
  --color-components-badge-status-light-success-border-inner: #47cd89;
  --color-components-badge-status-light-success-halo: rgb(23 178 106 / 0.3);

  --color-components-badge-status-light-border-outer: #222225;
  --color-components-badge-status-light-high-light: rgb(255 255 255 / 0.3);
  --color-components-badge-status-light-warning-bg: #f79009;
  --color-components-badge-status-light-warning-border-inner: #fdb022;
  --color-components-badge-status-light-warning-halo: rgb(247 144 9 / 0.3);

  --color-components-badge-status-light-error-bg: #f04438;
  --color-components-badge-status-light-error-border-inner: #f97066;
  --color-components-badge-status-light-error-halo: rgb(240 68 56 / 0.3);

  --color-components-badge-status-light-normal-bg: #0ba5ec;
  --color-components-badge-status-light-normal-border-inner: #36bffa;
  --color-components-badge-status-light-normal-halo: rgb(11 165 236 / 0.3);

  --color-components-badge-status-light-disabled-bg: #676f83;
  --color-components-badge-status-light-disabled-border-inner: #98a2b2;
  --color-components-badge-status-light-disabled-halo: rgb(200 206 218 / 0.08);

  --color-components-badge-bg-green-soft: rgb(23 178 106 / 0.14);
  --color-components-badge-bg-orange-soft: rgb(247 144 9 / 0.14);
  --color-components-badge-bg-red-soft: rgb(240 68 56 / 0.14);
  --color-components-badge-bg-blue-light-soft: rgb(11 165 236 / 0.14);
  --color-components-badge-bg-gray-soft: rgb(200 206 218 / 0.08);
  --color-components-badge-bg-dimm: rgb(255 255 255 / 0.03);

  --color-components-chart-line: #5289ff;
  --color-components-chart-area-1: rgb(21 90 239 / 0.2);
  --color-components-chart-area-2: rgb(21 90 239 / 0.04);
  --color-components-chart-current-1: #5289ff;
  --color-components-chart-current-2: rgb(21 90 239 / 0.3);
  --color-components-chart-bg: rgb(24 24 27 / 0.95);

  --color-components-actionbar-bg: #222225;
  --color-components-actionbar-border: rgb(200 206 218 / 0.08);
  --color-components-actionbar-bg-accent: #27272b;
  --color-components-actionbar-border-accent: #5289ff;

  --color-components-dropzone-bg-alt: rgb(24 24 27 / 0.8);
  --color-components-dropzone-bg: rgb(24 24 27 / 0.4);
  --color-components-dropzone-bg-accent: rgb(21 90 239 / 0.2);
  --color-components-dropzone-border: rgb(200 206 218 / 0.14);
  --color-components-dropzone-border-alt: rgb(200 206 218 / 0.2);
  --color-components-dropzone-border-accent: #84abff;

  --color-components-progress-brand-progress: #5289ff;
  --color-components-progress-brand-border: #5289ff;
  --color-components-progress-brand-bg: rgb(21 90 239 / 0.04);

  --color-components-progress-white-progress: #ffffff;
  --color-components-progress-white-border: rgb(255 255 255 / 0.95);
  --color-components-progress-white-bg: rgb(255 255 255 / 0.01);

  --color-components-progress-gray-progress: #98a2b2;
  --color-components-progress-gray-border: #98a2b2;
  --color-components-progress-gray-bg: rgb(200 206 218 / 0.02);

  --color-components-progress-warning-progress: #fdb022;
  --color-components-progress-warning-border: #fdb022;
  --color-components-progress-warning-bg: rgb(247 144 9 / 0.04);

  --color-components-progress-error-progress: #f97066;
  --color-components-progress-error-border: #f97066;
  --color-components-progress-error-bg: rgb(240 68 56 / 0.04);

  --color-components-chat-input-audio-bg: rgb(21 90 239 / 0.2);
  --color-components-chat-input-audio-wave-default: rgb(200 206 218 / 0.14);
  --color-components-chat-input-bg-mask-1: rgb(24 24 27 / 0.04);
  --color-components-chat-input-bg-mask-2: rgb(24 24 27 / 0.6);
  --color-components-chat-input-border: rgb(200 206 218 / 0.2);
  --color-components-chat-input-audio-wave-active: #84abff;
  --color-components-chat-input-audio-bg-alt: rgb(24 24 27 / 0.9);

  --color-components-avatar-shape-fill-stop-0: rgb(255 255 255 / 0.95);
  --color-components-avatar-shape-fill-stop-100: rgb(255 255 255 / 0.8);

  --color-components-avatar-bg-mask-stop-0: rgb(255 255 255 / 0.2);
  --color-components-avatar-bg-mask-stop-100: rgb(255 255 255 / 0.03);

  --color-components-avatar-default-avatar-bg: #222225;
  --color-components-avatar-mask-darkmode-dimmed: rgb(0 0 0 / 0.12);

  --color-components-label-gray: rgb(200 206 218 / 0.14);

  --color-components-premium-badge-blue-bg-stop-0: #5289ff;
  --color-components-premium-badge-blue-bg-stop-100: #296dff;
  --color-components-premium-badge-blue-stroke-stop-0: rgb(255 255 255 / 0.2);
  --color-components-premium-badge-blue-stroke-stop-100: #296dff;
  --color-components-premium-badge-blue-text-stop-0: #eff4ff;
  --color-components-premium-badge-blue-text-stop-100: #b2caff;
  --color-components-premium-badge-blue-glow: #004aeb;
  --color-components-premium-badge-blue-bg-stop-0-hover: #84abff;
  --color-components-premium-badge-blue-bg-stop-100-hover: #004aeb;
  --color-components-premium-badge-blue-glow-hover: #d1e0ff;
  --color-components-premium-badge-blue-stroke-stop-0-hover: rgb(255 255 255 / 0.5);
  --color-components-premium-badge-blue-stroke-stop-100-hover: #296dff;

  --color-components-premium-badge-highlight-stop-0: rgb(255 255 255 / 0.12);
  --color-components-premium-badge-highlight-stop-100: rgb(255 255 255 / 0.2);
  --color-components-premium-badge-indigo-bg-stop-0: #6172f3;
  --color-components-premium-badge-indigo-bg-stop-100: #3538cd;
  --color-components-premium-badge-indigo-stroke-stop-0: rgb(255 255 255 / 0.2);
  --color-components-premium-badge-indigo-stroke-stop-100: #444ce7;
  --color-components-premium-badge-indigo-text-stop-0: #eef4ff;
  --color-components-premium-badge-indigo-text-stop-100: #c7d7fe;
  --color-components-premium-badge-indigo-glow: #3538cd;
  --color-components-premium-badge-indigo-glow-hover: #e0eaff;
  --color-components-premium-badge-indigo-bg-stop-0-hover: #a4bcfd;
  --color-components-premium-badge-indigo-bg-stop-100-hover: #3538cd;
  --color-components-premium-badge-indigo-stroke-stop-0-hover: rgb(255 255 255 / 0.5);
  --color-components-premium-badge-indigo-stroke-stop-100-hover: #444ce7;

  --color-components-premium-badge-grey-bg-stop-0: #676f83;
  --color-components-premium-badge-grey-bg-stop-100: #495464;
  --color-components-premium-badge-grey-stroke-stop-0: rgb(255 255 255 / 0.12);
  --color-components-premium-badge-grey-stroke-stop-100: #495464;
  --color-components-premium-badge-grey-text-stop-0: #f9fafb;
  --color-components-premium-badge-grey-text-stop-100: #e9ebf0;
  --color-components-premium-badge-grey-glow: #354052;
  --color-components-premium-badge-grey-glow-hover: #f2f4f7;
  --color-components-premium-badge-grey-bg-stop-0-hover: #98a2b2;
  --color-components-premium-badge-grey-bg-stop-100-hover: #354052;
  --color-components-premium-badge-grey-stroke-stop-0-hover: rgb(255 255 255 / 0.5);
  --color-components-premium-badge-grey-stroke-stop-100-hover: #676f83;

  --color-components-premium-badge-orange-bg-stop-0: #ff692e;
  --color-components-premium-badge-orange-bg-stop-100: #e04f16;
  --color-components-premium-badge-orange-stroke-stop-0: rgb(255 255 255 / 0.2);
  --color-components-premium-badge-orange-stroke-stop-100: #ff4405;
  --color-components-premium-badge-orange-text-stop-0: #fef6ee;
  --color-components-premium-badge-orange-text-stop-100: #f9dbaf;
  --color-components-premium-badge-orange-glow: #b93815;
  --color-components-premium-badge-orange-glow-hover: #fdead7;
  --color-components-premium-badge-orange-bg-stop-0-hover: #ff692e;
  --color-components-premium-badge-orange-bg-stop-100-hover: #b93815;
  --color-components-premium-badge-orange-stroke-stop-0-hover: rgb(255 255 255 / 0.5);
  --color-components-premium-badge-orange-stroke-stop-100-hover: #ff4405;

  --color-components-progress-bar-bg: rgb(200 206 218 / 0.08);
  --color-components-progress-bar-progress: rgb(200 206 218 / 0.14);
  --color-components-progress-bar-border: rgb(255 255 255 / 0.03);
  --color-components-progress-bar-progress-solid: rgb(255 255 255 / 0.95);
  --color-components-progress-bar-progress-highlight: rgb(200 206 218 / 0.2);

  --color-components-icon-bg-red-solid: #d92d20;
  --color-components-icon-bg-rose-solid: #e31b54;
  --color-components-icon-bg-pink-solid: #dd2590;
  --color-components-icon-bg-orange-dark-solid: #ff4405;
  --color-components-icon-bg-yellow-solid: #eaaa08;
  --color-components-icon-bg-green-solid: #4ca30d;
  --color-components-icon-bg-teal-solid: #0e9384;
  --color-components-icon-bg-blue-light-solid: #0ba5ec;
  --color-components-icon-bg-blue-solid: #155aef;
  --color-components-icon-bg-indigo-solid: #444ce7;
  --color-components-icon-bg-violet-solid: #7839ee;
  --color-components-icon-bg-midnight-solid: #5d698d;
  --color-components-icon-bg-rose-soft: rgb(246 61 104 / 0.2);
  --color-components-icon-bg-pink-soft: rgb(238 70 188 / 0.2);
  --color-components-icon-bg-orange-dark-soft: rgb(255 68 5 / 0.2);
  --color-components-icon-bg-yellow-soft: rgb(234 170 8 / 0.2);
  --color-components-icon-bg-green-soft: rgb(102 198 28 / 0.2);
  --color-components-icon-bg-teal-soft: rgb(21 183 158 / 0.2);
  --color-components-icon-bg-blue-light-soft: rgb(11 165 236 / 0.2);
  --color-components-icon-bg-blue-soft: rgb(21 90 239 / 0.2);
  --color-components-icon-bg-indigo-soft: rgb(97 114 243 / 0.2);
  --color-components-icon-bg-violet-soft: rgb(135 91 247 / 0.2);
  --color-components-icon-bg-midnight-soft: rgb(130 141 173 / 0.2);
  --color-components-icon-bg-red-soft: rgb(240 68 56 / 0.2);
  --color-components-icon-bg-orange-solid: #f79009;
  --color-components-icon-bg-orange-soft: rgb(247 144 9 / 0.2);

  --color-text-primary: #fbfbfc;
  --color-text-secondary: #d9d9de;
  --color-text-tertiary: rgb(200 206 218 / 0.6);
  --color-text-quaternary: rgb(200 206 218 / 0.4);
  --color-text-destructive: #f97066;
  --color-text-success: #17b26a;
  --color-text-warning: #f79009;
  --color-text-destructive-secondary: #f97066;
  --color-text-success-secondary: #47cd89;
  --color-text-warning-secondary: #fdb022;
  --color-text-accent: #5289ff;
  --color-text-primary-on-surface: rgb(255 255 255 / 0.95);
  --color-text-placeholder: rgb(200 206 218 / 0.3);
  --color-text-disabled: rgb(200 206 218 / 0.3);
  --color-text-accent-secondary: #84abff;
  --color-text-accent-light-mode-only: #d9d9de;
  --color-text-text-selected: rgb(21 90 239 / 0.3);
  --color-text-secondary-on-surface: rgb(255 255 255 / 0.9);
  --color-text-logo-text: #e9e9ec;
  --color-text-empty-state-icon: rgb(200 206 218 / 0.3);
  --color-text-inverted: #ffffff;
  --color-text-inverted-dimmed: rgb(255 255 255 / 0.8);

  --color-background-body: #1d1d20;
  --color-background-default-subtle: #222225;
  --color-background-neutral-subtle: #1d1d20;
  --color-background-sidenav-bg: rgb(39 39 42 / 0.92);
  --color-background-default: #222225;
  --color-background-soft: rgb(24 24 27 / 0.25);
  --color-background-gradient-bg-fill-chat-bg-1: #222225;
  --color-background-gradient-bg-fill-chat-bg-2: #1d1d20;
  --color-background-gradient-bg-fill-chat-bubble-bg-1: rgb(200 206 218 / 0.08);
  --color-background-gradient-bg-fill-chat-bubble-bg-2: rgb(200 206 218 / 0.02);
  --color-background-gradient-bg-fill-debug-bg-1: rgb(200 206 218 / 0.08);
  --color-background-gradient-bg-fill-debug-bg-2: rgb(24 24 27 / 0.04);

  --color-background-gradient-mask-gray: rgb(24 24 27 / 0.08);
  --color-background-gradient-mask-transparent: rgb(0 0 0 / 0);
  --color-background-gradient-mask-input-clear-2: rgb(57 58 62 / 0);
  --color-background-gradient-mask-input-clear-1: #393a3e;
  --color-background-gradient-mask-transparent-dark: rgb(0 0 0 / 0);
  --color-background-gradient-mask-side-panel-2: rgb(24 24 27 / 0.9);
  --color-background-gradient-mask-side-panel-1: rgb(24 24 27 / 0.04);

  --color-background-default-burn: #1d1d20;
  --color-background-overlay-fullscreen: rgb(39 39 42 / 0.97);
  --color-background-default-lighter: rgb(200 206 218 / 0.04);
  --color-background-section: rgb(24 24 27 / 0.4);
  --color-background-interaction-from-bg-1: rgb(24 24 27 / 0.4);
  --color-background-interaction-from-bg-2: rgb(24 24 27 / 0.14);
  --color-background-section-burn: rgb(24 24 27 / 0.6);
  --color-background-default-dodge: #3a3a40;
  --color-background-overlay: rgb(24 24 27 / 0.8);
  --color-background-default-dimmed: #27272b;
  --color-background-default-hover: #27272b;
  --color-background-overlay-alt: rgb(24 24 27 / 0.4);
  --color-background-surface-white: rgb(255 255 255 / 0.9);
  --color-background-overlay-destructive: rgb(240 68 56 / 0.3);
  --color-background-overlay-backdrop: rgb(24 24 27 / 0.95);
  --color-background-body-transparent: rgb(29 29 32 / 0);

  --color-shadow-shadow-1: rgb(0 0 0 / 0.05);
  --color-shadow-shadow-3: rgb(0 0 0 / 0.1);
  --color-shadow-shadow-4: rgb(0 0 0 / 0.12);
  --color-shadow-shadow-5: rgb(0 0 0 / 0.16);
  --color-shadow-shadow-6: rgb(0 0 0 / 0.2);
  --color-shadow-shadow-7: rgb(0 0 0 / 0.24);
  --color-shadow-shadow-8: rgb(0 0 0 / 0.28);
  --color-shadow-shadow-9: rgb(0 0 0 / 0.36);
  --color-shadow-shadow-2: rgb(0 0 0 / 0.08);
  --color-shadow-shadow-10: rgb(0 0 0 / 0.4);

  --color-workflow-block-border: rgb(255 255 255 / 0.08);
  --color-workflow-block-parma-bg: rgb(255 255 255 / 0.05);
  --color-workflow-block-bg: #27272b;
  --color-workflow-block-bg-transparent: rgb(39 39 43 / 0.96);
  --color-workflow-block-border-highlight: rgb(200 206 218 / 0.2);
  --color-workflow-block-wrapper-bg-1: #27272b;
  --color-workflow-block-wrapper-bg-2: rgb(39 39 43 / 0.2);

  --color-workflow-canvas-workflow-dot-color: rgb(133 133 173 / 0.11);
  --color-workflow-canvas-workflow-bg: #1d1d20;
  --color-workflow-canvas-workflow-top-bar-1: rgb(29 29 32 / 0.9);
  --color-workflow-canvas-workflow-top-bar-2: rgb(29 29 32 / 0.08);
  --color-workflow-canvas-canvas-overlay: rgb(29 29 32 / 0.8);

  --color-workflow-link-line-active: #5289ff;
  --color-workflow-link-line-normal: #676f83;
  --color-workflow-link-line-handle: #5289ff;
  --color-workflow-link-line-normal-transparent: rgb(103 111 131 / 0.2);
  --color-workflow-link-line-failure-active: #fdb022;
  --color-workflow-link-line-failure-handle: #fdb022;
  --color-workflow-link-line-failure-button-bg: #f79009;
  --color-workflow-link-line-failure-button-hover: #dc6803;

  --color-workflow-link-line-success-active: #47cd89;
  --color-workflow-link-line-success-handle: #47cd89;

  --color-workflow-link-line-error-active: #f97066;
  --color-workflow-link-line-error-handle: #f97066;

  --color-workflow-minimap-bg: #27272b;
  --color-workflow-minimap-block: rgb(200 206 218 / 0.08);

  --color-workflow-display-success-bg: rgb(23 178 106 / 0.2);
  --color-workflow-display-success-border-1: rgb(23 178 106 / 0.9);
  --color-workflow-display-success-border-2: rgb(23 178 106 / 0.8);
  --color-workflow-display-success-vignette-color: rgb(23 178 106 / 0.25);
  --color-workflow-display-success-bg-line-pattern: rgb(24 24 27 / 0.8);

  --color-workflow-display-glass-1: rgb(255 255 255 / 0.03);
  --color-workflow-display-glass-2: rgb(255 255 255 / 0.05);
  --color-workflow-display-vignette-dark: rgb(0 0 0 / 0.4);
  --color-workflow-display-highlight: rgb(255 255 255 / 0.12);
  --color-workflow-display-outline: rgb(24 24 27 / 0.95);
  --color-workflow-display-error-bg: rgb(240 68 56 / 0.2);
  --color-workflow-display-error-bg-line-pattern: rgb(24 24 27 / 0.8);
  --color-workflow-display-error-border-1: rgb(240 68 56 / 0.9);
  --color-workflow-display-error-border-2: rgb(240 68 56 / 0.8);
  --color-workflow-display-error-vignette-color: rgb(240 68 56 / 0.25);

  --color-workflow-display-warning-bg: rgb(247 144 9 / 0.2);
  --color-workflow-display-warning-bg-line-pattern: rgb(24 24 27 / 0.8);
  --color-workflow-display-warning-border-1: rgb(247 144 9 / 0.9);
  --color-workflow-display-warning-border-2: rgb(247 144 9 / 0.8);
  --color-workflow-display-warning-vignette-color: rgb(247 144 9 / 0.25);

  --color-workflow-display-normal-bg: rgb(11 165 236 / 0.2);
  --color-workflow-display-normal-bg-line-pattern: rgb(24 24 27 / 0.8);
  --color-workflow-display-normal-border-1: rgb(11 165 236 / 0.9);
  --color-workflow-display-normal-border-2: rgb(11 165 236 / 0.8);
  --color-workflow-display-normal-vignette-color: rgb(11 165 236 / 0.25);

  --color-workflow-display-disabled-bg: rgb(200 206 218 / 0.2);
  --color-workflow-display-disabled-bg-line-pattern: rgb(24 24 27 / 0.8);
  --color-workflow-display-disabled-border-1: rgb(200 206 218 / 0.6);
  --color-workflow-display-disabled-border-2: rgb(200 206 218 / 0.25);
  --color-workflow-display-disabled-vignette-color: rgb(200 206 218 / 0.25);
  --color-workflow-display-disabled-outline: rgb(24 24 27 / 0.95);

  --color-workflow-workflow-progress-bg-1: rgb(24 24 27 / 0.25);
  --color-workflow-workflow-progress-bg-2: rgb(24 24 27 / 0.04);

  --color-divider-subtle: rgb(200 206 218 / 0.08);
  --color-divider-regular: rgb(200 206 218 / 0.14);
  --color-divider-deep: rgb(200 206 218 / 0.2);
  --color-divider-burn: rgb(24 24 27 / 0.95);
  --color-divider-intense: rgb(200 206 218 / 0.4);
  --color-divider-solid: #3a3a40;
  --color-divider-solid-alt: #747481;
  --color-divider-accent: rgb(200 206 218 / 0.14);

  --color-state-base-hover: rgb(200 206 218 / 0.08);
  --color-state-base-active: rgb(200 206 218 / 0.2);
  --color-state-base-hover-alt: rgb(200 206 218 / 0.14);
  --color-state-base-handle: rgb(200 206 218 / 0.3);
  --color-state-base-handle-hover: rgb(200 206 218 / 0.5);
  --color-state-base-hover-subtle: rgb(200 206 218 / 0.04);

  --color-state-accent-hover: rgb(21 90 239 / 0.14);
  --color-state-accent-active: rgb(21 90 239 / 0.14);
  --color-state-accent-hover-alt: rgb(21 90 239 / 0.25);
  --color-state-accent-solid: #5289ff;
  --color-state-accent-active-alt: rgb(21 90 239 / 0.2);

  --color-state-destructive-hover: rgb(240 68 56 / 0.14);
  --color-state-destructive-hover-alt: rgb(240 68 56 / 0.25);
  --color-state-destructive-active: rgb(240 68 56 / 0.3);
  --color-state-destructive-solid: #f97066;
  --color-state-destructive-border: #f97066;
  --color-state-destructive-hover-transparent: rgb(240 68 56 / 0);

  --color-state-success-hover: rgb(23 178 106 / 0.14);
  --color-state-success-hover-alt: rgb(23 178 106 / 0.25);
  --color-state-success-active: rgb(23 178 106 / 0.3);
  --color-state-success-solid: #47cd89;

  --color-state-warning-hover: rgb(247 144 9 / 0.14);
  --color-state-warning-hover-alt: rgb(247 144 9 / 0.25);
  --color-state-warning-active: rgb(247 144 9 / 0.3);
  --color-state-warning-solid: #f79009;
  --color-state-warning-hover-transparent: rgb(247 144 9 / 0);

  --color-effects-highlight: rgb(200 206 218 / 0.08);
  --color-effects-highlight-lightmode-off: rgb(200 206 218 / 0.08);
  --color-effects-image-frame: #ffffff;
  --color-effects-icon-border: rgb(255 255 255 / 0.15);

  --color-util-colors-orange-dark-orange-dark-50: #57130a;
  --color-util-colors-orange-dark-orange-dark-100: #771a0d;
  --color-util-colors-orange-dark-orange-dark-200: #97180c;
  --color-util-colors-orange-dark-orange-dark-300: #bc1b06;
  --color-util-colors-orange-dark-orange-dark-400: #e62e05;
  --color-util-colors-orange-dark-orange-dark-500: #ff4405;
  --color-util-colors-orange-dark-orange-dark-600: #ff692e;
  --color-util-colors-orange-dark-orange-dark-700: #ff9c66;

  --color-util-colors-orange-orange-50: #511c10;
  --color-util-colors-orange-orange-100: #772917;
  --color-util-colors-orange-orange-200: #932f19;
  --color-util-colors-orange-orange-300: #b93815;
  --color-util-colors-orange-orange-400: #e04f16;
  --color-util-colors-orange-orange-500: #ef6820;
  --color-util-colors-orange-orange-600: #f38744;
  --color-util-colors-orange-orange-700: #f7b27a;
  --color-util-colors-orange-orange-100-transparent: rgb(119 41 23 / 0);

  --color-util-colors-pink-pink-50: #4e0d30;
  --color-util-colors-pink-pink-100: #851651;
  --color-util-colors-pink-pink-200: #9e165f;
  --color-util-colors-pink-pink-300: #c11574;
  --color-util-colors-pink-pink-400: #dd2590;
  --color-util-colors-pink-pink-500: #ee46bc;
  --color-util-colors-pink-pink-600: #f670c7;
  --color-util-colors-pink-pink-700: #faa7e0;

  --color-util-colors-fuchsia-fuchsia-50: #47104c;
  --color-util-colors-fuchsia-fuchsia-100: #6f1877;
  --color-util-colors-fuchsia-fuchsia-200: #821890;
  --color-util-colors-fuchsia-fuchsia-300: #9f1ab1;
  --color-util-colors-fuchsia-fuchsia-400: #ba24d5;
  --color-util-colors-fuchsia-fuchsia-500: #d444f1;
  --color-util-colors-fuchsia-fuchsia-600: #e478fa;
  --color-util-colors-fuchsia-fuchsia-700: #eeaafd;

  --color-util-colors-purple-purple-50: #27115f;
  --color-util-colors-purple-purple-100: #3e1c96;
  --color-util-colors-purple-purple-200: #4a1fb8;
  --color-util-colors-purple-purple-300: #5925dc;
  --color-util-colors-purple-purple-400: #6938ef;
  --color-util-colors-purple-purple-500: #7a5af8;
  --color-util-colors-purple-purple-600: #9b8afb;
  --color-util-colors-purple-purple-700: #bdb4fe;

  --color-util-colors-indigo-indigo-50: #1f235b;
  --color-util-colors-indigo-indigo-100: #2d3282;
  --color-util-colors-indigo-indigo-200: #2d31a6;
  --color-util-colors-indigo-indigo-300: #3538cd;
  --color-util-colors-indigo-indigo-400: #444ce7;
  --color-util-colors-indigo-indigo-500: #6172f3;
  --color-util-colors-indigo-indigo-600: #8098f9;
  --color-util-colors-indigo-indigo-700: #a4bcfd;

  --color-util-colors-blue-blue-50: #102a56;
  --color-util-colors-blue-blue-100: #194185;
  --color-util-colors-blue-blue-200: #1849a9;
  --color-util-colors-blue-blue-300: #175cd3;
  --color-util-colors-blue-blue-400: #1570ef;
  --color-util-colors-blue-blue-500: #2e90fa;
  --color-util-colors-blue-blue-600: #53b1fd;
  --color-util-colors-blue-blue-700: #84caff;

  --color-util-colors-blue-light-blue-light-50: #062c41;
  --color-util-colors-blue-light-blue-light-100: #0b4a6f;
  --color-util-colors-blue-light-blue-light-200: #065986;
  --color-util-colors-blue-light-blue-light-300: #026aa2;
  --color-util-colors-blue-light-blue-light-400: #0086c9;
  --color-util-colors-blue-light-blue-light-500: #0ba5ec;
  --color-util-colors-blue-light-blue-light-600: #36bffa;
  --color-util-colors-blue-light-blue-light-700: #7cd4fd;

  --color-util-colors-gray-blue-gray-blue-50: #0d0f1c;
  --color-util-colors-gray-blue-gray-blue-100: #101323;
  --color-util-colors-gray-blue-gray-blue-200: #293056;
  --color-util-colors-gray-blue-gray-blue-300: #363f72;
  --color-util-colors-gray-blue-gray-blue-400: #3e4784;
  --color-util-colors-gray-blue-gray-blue-500: #4e5ba6;
  --color-util-colors-gray-blue-gray-blue-600: #717bbc;
  --color-util-colors-gray-blue-gray-blue-700: #b3b8db;

  --color-util-colors-blue-brand-blue-brand-50: #002066;
  --color-util-colors-blue-brand-blue-brand-100: #00329e;
  --color-util-colors-blue-brand-blue-brand-200: #003dc1;
  --color-util-colors-blue-brand-blue-brand-300: #004aeb;
  --color-util-colors-blue-brand-blue-brand-400: #155aef;
  --color-util-colors-blue-brand-blue-brand-500: #296dff;
  --color-util-colors-blue-brand-blue-brand-600: #5289ff;
  --color-util-colors-blue-brand-blue-brand-700: #84abff;

  --color-util-colors-red-red-50: #55160c;
  --color-util-colors-red-red-100: #7a271a;
  --color-util-colors-red-red-200: #912018;
  --color-util-colors-red-red-300: #b42318;
  --color-util-colors-red-red-400: #d92d20;
  --color-util-colors-red-red-500: #f04438;
  --color-util-colors-red-red-600: #f97066;
  --color-util-colors-red-red-700: #fda29b;

  --color-util-colors-green-green-50: #053321;
  --color-util-colors-green-green-100: #074d31;
  --color-util-colors-green-green-200: #085d3a;
  --color-util-colors-green-green-300: #067647;
  --color-util-colors-green-green-400: #079455;
  --color-util-colors-green-green-500: #17b26a;
  --color-util-colors-green-green-600: #47cd89;
  --color-util-colors-green-green-700: #75e0a7;

  --color-util-colors-warning-warning-50: #4e1d09;
  --color-util-colors-warning-warning-100: #7a2e0e;
  --color-util-colors-warning-warning-200: #93370d;
  --color-util-colors-warning-warning-300: #b54708;
  --color-util-colors-warning-warning-400: #dc6803;
  --color-util-colors-warning-warning-500: #f79009;
  --color-util-colors-warning-warning-600: #fdb022;
  --color-util-colors-warning-warning-700: #fec84b;

  --color-util-colors-yellow-yellow-50: #542c0d;
  --color-util-colors-yellow-yellow-100: #713b12;
  --color-util-colors-yellow-yellow-200: #854a0e;
  --color-util-colors-yellow-yellow-300: #a15c07;
  --color-util-colors-yellow-yellow-400: #ca8504;
  --color-util-colors-yellow-yellow-500: #eaaa08;
  --color-util-colors-yellow-yellow-600: #fac515;
  --color-util-colors-yellow-yellow-700: #fde272;

  --color-util-colors-teal-teal-50: #0a2926;
  --color-util-colors-teal-teal-100: #134e48;
  --color-util-colors-teal-teal-200: #125d56;
  --color-util-colors-teal-teal-300: #107569;
  --color-util-colors-teal-teal-400: #0e9384;
  --color-util-colors-teal-teal-500: #15b79e;
  --color-util-colors-teal-teal-600: #2ed3b7;
  --color-util-colors-teal-teal-700: #5fe9d0;

  --color-util-colors-cyan-cyan-50: #0d2d3a;
  --color-util-colors-cyan-cyan-100: #164c63;
  --color-util-colors-cyan-cyan-200: #155b75;
  --color-util-colors-cyan-cyan-300: #0e7090;
  --color-util-colors-cyan-cyan-400: #088ab2;
  --color-util-colors-cyan-cyan-500: #06aed4;
  --color-util-colors-cyan-cyan-600: #22ccee;
  --color-util-colors-cyan-cyan-700: #67e3f9;

  --color-util-colors-violet-violet-50: #2e125e;
  --color-util-colors-violet-violet-100: #491c96;
  --color-util-colors-violet-violet-200: #5720b7;
  --color-util-colors-violet-violet-300: #6927da;
  --color-util-colors-violet-violet-400: #7839ee;
  --color-util-colors-violet-violet-500: #875bf7;
  --color-util-colors-violet-violet-600: #a48afb;
  --color-util-colors-violet-violet-700: #c3b5fd;

  --color-util-colors-gray-gray-50: #0c111c;
  --color-util-colors-gray-gray-100: #101828;
  --color-util-colors-gray-gray-200: #18222f;
  --color-util-colors-gray-gray-300: #354052;
  --color-util-colors-gray-gray-400: #495464;
  --color-util-colors-gray-gray-500: #676f83;
  --color-util-colors-gray-gray-600: #98a2b2;
  --color-util-colors-gray-gray-700: #d0d5dc;

  --color-util-colors-green-light-green-light-50: #15290a;
  --color-util-colors-green-light-green-light-100: #2b5314;
  --color-util-colors-green-light-green-light-200: #326212;
  --color-util-colors-green-light-green-light-300: #3b7c0f;
  --color-util-colors-green-light-green-light-500: #66c61c;
  --color-util-colors-green-light-green-light-400: #4ca30d;
  --color-util-colors-green-light-green-light-600: #85e13a;
  --color-util-colors-green-light-green-light-700: #a6ef67;

  --color-util-colors-rose-rose-50: #510b24;
  --color-util-colors-rose-rose-100: #89123e;
  --color-util-colors-rose-rose-200: #a11043;
  --color-util-colors-rose-rose-300: #c01048;
  --color-util-colors-rose-rose-400: #e31b54;
  --color-util-colors-rose-rose-500: #f63d68;
  --color-util-colors-rose-rose-600: #fd6f8e;
  --color-util-colors-rose-rose-700: #fea3b4;

  --color-util-colors-midnight-midnight-50: #171c22;
  --color-util-colors-midnight-midnight-100: #202431;
  --color-util-colors-midnight-midnight-200: #2f3648;
  --color-util-colors-midnight-midnight-300: #3e465e;
  --color-util-colors-midnight-midnight-400: #5d698d;
  --color-util-colors-midnight-midnight-500: #828dad;
  --color-util-colors-midnight-midnight-600: #a7aec5;
  --color-util-colors-midnight-midnight-700: #c6cbd9;

  --color-third-party-LangChain: #ffffff;
  --color-third-party-Langfuse: #ffffff;
  --color-third-party-Github: #ffffff;
  --color-third-party-Github-tertiary: rgb(200 206 218 / 0.6);
  --color-third-party-Github-secondary: #d9d9de;
  --color-third-party-model-bg-openai: #121212;
  --color-third-party-model-bg-anthropic: #1d1917;
  --color-third-party-model-bg-default: #1d1d20;

  --color-third-party-aws: #141f2e;
  --color-third-party-aws-alt: #192639;

  --color-saas-background: #0b0b0e;
  --color-saas-pricing-grid-bg: rgb(200 206 218 / 0.2);
  --color-saas-dify-blue-static: #0033ff;
  --color-saas-dify-blue-static-hover: #002cd6;
  --color-saas-dify-blue-accessible: #0a68ff;
  --color-saas-dify-blue-inverted: #ffffff;
  --color-saas-dify-blue-inverted-dimmed: rgb(255 255 255 / 0.88);

  --color-saas-background-inverted: rgb(255 255 255 / 0.9);
  --color-saas-background-inverted-hover: #ffffff;

  --color-dify-logo-dify-logo-blue: #e8e8e8;
  --color-dify-logo-dify-logo-black: #e8e8e8;

}
